/* marketing agency page css */
.body-marketing-agency {
    background-color: #FCF7F3;

    &.dark {
        --border: rgba(252, 247, 243, 0.1);
    }

    .container {
        &.large {
            @media (min-width: 1850px) {
                max-width: 1850px;
            }
        }
    }

    .section-title {

        .mb-14 {
            transform: translate(0px, -14px);
            display: inline-block;

            @media #{$lg} {
                display: block;
                transform: none;
            }

            @media #{$sm} {
                margin-top: 10px;
            }
        }

    }
}

/* hero area 3 style  */
.hero-area-3 {
    &-inner {
        padding-top: 28px;
        padding-bottom: 100px;
    }

    .section-subtitle {
        font-size: 18px;
        font-weight: 400;
        line-height: 22px;
        text-transform: unset;
        display: inline-block;
        max-width: 231px;
        color: var(--primary);
        text-transform: math-auto;
    }

    .subtitle-wrapper {
        max-width: 1090px;
        margin-left: auto;

        @media #{$xxl} {
            max-width: 790px;
        }

        @media #{$xl} {
            max-width: 690px;
        }

        @media #{$lg} {
            max-width: 590px;
        }

        @media #{$md} {
            margin-left: 0;
            margin-top: 70px;
        }
    }

    .section-title {
        font-size: 140px;
        font-weight: 310;
        line-height: 0.85;
        letter-spacing: -0.07em;
        max-width: 1240px;

        @media #{$xxl} {
            font-size: 100px;
            max-width: 940px;
        }

        @media #{$lg} {
            font-size: 70px;
            max-width: 640px;
        }

        @media #{$sm} {
            font-size: 50px;
        }

        .title-shape-1 {
            height: 96px;
            margin-right: 15px;

            @media #{$xxl} {
                height: 76px;
                margin-right: 10px;
            }

            @media #{$lg} {
                height: 51px;
            }

            @media #{$sm} {
                height: 36px;
                margin-right: 5px;
            }
        }

        .title-shape-2 {
            height: 84px;
            margin-left: 5px;
            margin-top: -10px;

            @media #{$xxl} {
                height: 64px;
            }

            @media #{$lg} {
                height: 44px;
                margin-top: -6px;
            }

            @media #{$sm} {
                height: 30px;
            }
        }

        .text-underline {
            color: rgba(17, 17, 17, 0.2);
            text-decoration-line: underline;
            text-decoration-thickness: 7px;
            text-underline-offset: 12px;
            position: relative;
            cursor: pointer;

            @include dark {
                color: rgba(252, 247, 243, 0.2);
            }

            @media #{$xxl} {
                text-decoration-thickness: 6px;
                text-underline-offset: 8px;
            }

            @media #{$lg} {
                text-decoration-thickness: 4px;
                text-underline-offset: 6px;
            }

            @media #{$sm} {
                text-decoration-thickness: 3px;
                text-underline-offset: 4px;
            }

            .hover-image {
                position: absolute;
                top: 0;
                left: 0;
                width: 200px;
                height: 250px;
                cursor: pointer;
                opacity: 0;
                transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
            }

            .text-underline:hover .hover-image {
                opacity: 1;
            }
        }
    }

    .title-wrapper {
        margin-top: 43px;

        @media #{$md} {
            margin-top: 23px;
        }
    }

    .social-links {
        display: flex;
        gap: 8px 20px;
        max-width: 270px;
        flex-wrap: wrap;
        align-self: flex-end;

        @media #{$md} {
            max-width: 100%;
        }

        li {
            font-size: 18px;
            font-weight: 400;
            line-height: 22px;
            position: relative;
            text-decoration-style: solid;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: var(--primary);

            &::before {
                width: 100%;
                height: 1px;
                background-color: currentColor;
                content: "";
                position: absolute;
                left: 0;
                bottom: 0;
                transition: all 0.5s;
            }

            &:hover {
                &::before {
                    width: 0;
                }
            }
        }
    }

    .info-text {
        font-size: 18px;
        font-weight: 400;
        line-height: 22px;
        color: var(--primary);
        max-width: 140px;
    }

    .about-text {
        font-size: 20px;
        font-weight: 400;
        line-height: 28px;
        color: var(--primary);
        max-width: 410px;

        @media #{$md} {
            max-width: 100%;
        }
    }

    .section-content {
        display: grid;
        gap: 30px 50px;
        grid-template-columns: 1fr 310px 730px;
        margin-top: 88px;

        @media #{$xxl} {
            grid-template-columns: 1fr 310px 430px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 310px 330px;
            margin-top: 58px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 210px 330px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }

        .btn-wrapper {
            margin-top: 56px;

            @media #{$lg} {
                margin-top: 36px;
            }
        }
    }
}

.text-underline {
    position: relative;
    cursor: pointer;
}

.image-hover {
    position: fixed;
    top: 0;
    left: 0;
    width: 200px;
    height: 250px;
    pointer-events: none;
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.3s ease, transform 0.3s ease;

    @media (max-width: 768px) {
        display: none;
    }
}

.text-underline:hover .image-hover {
    opacity: 1;
    transform: scale(1);
}

/* service area 3 style  */
.service-area-3 {
    .section-header {
        margin-top: 50px;

        .rr-btn-group.b {
            padding: 9px 18px !important;
        }
    }

    .services-wrapper-box {
        margin-top: 86px;

        @media #{$xxl} {
            margin-top: 56px;
        }

        @media #{$xl} {
            margin-top: 36px;
        }
    }
}

.services-wrapper-3 {
    display: grid;
    gap: 30px 60px;
    grid-template-columns: repeat(4, 1fr);
    overflow: hidden;

    @media #{$xxl} {
        gap: 30px 40px;
    }

    @media #{$md} {
        grid-template-columns: repeat(2, 1fr);
    }

    @media #{$xs} {
        grid-template-columns: repeat(1, 1fr);
    }

    .service-box {
        display: grid;
        gap: 20px 30px;
        grid-template-columns: 110px 1fr;
        align-items: flex-start;
        padding-top: 12px;
        border-top: 1px solid var(--border);
        position: relative;

        @media #{$xxl} {
            gap: 20px 20px;
            grid-template-columns: 70px 1fr;
        }

        @media #{$xl} {
            grid-template-columns: 1fr;
        }

        &:hover {
            .thumb {
                img {
                    transform: scale(1.1);
                }
            }
        }

        &:before {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: var(--border);
            top: 0;
            left: -30px;

            @media #{$xxl} {
                left: -20px;
            }
        }

        .thumb {
            margin-top: 8px;
            border-radius: 25px;
            overflow: hidden;

            @media #{$xxl} {
                border-radius: 15px;
            }

            @media #{$xl} {
                max-width: 70px;
            }

            img {
                width: 100%;
                transition: all 0.5s;
            }
        }

        .title {
            font-size: 30px;
            font-weight: 310;
            line-height: 1.16;
            letter-spacing: -0.07em;

            @media #{$xxl} {
                font-size: 24px;
            }
        }

        .text {
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
            margin-top: 18px;
        }

        .rr-btn-underline {
            text-transform: unset;
            font-size: 18px;
            color: var(--primary);
            margin-top: 50px;
            display: inline-block;
            position: relative;

            &::before {
                content: "";
                width: 100%;
                height: 1px;
                background-color: currentColor;
                position: absolute;
                left: 0;
                bottom: 0;
                transition: all 0.4s;
            }

            &:hover {

                &::before {
                    width: 0;
                }
            }

            @media #{$xxl} {
                margin-top: 30px;
            }
        }
    }
}

/* work area 3 style  */
.work-area-3 {

    .section-title {
        max-width: 1060px;

        @media #{$xxl} {
            max-width: 860px;
        }

        .rr-btn-group {

            &:hover {
                .c {
                    transform: translate(-11px, 0px);
                }
            }
        }
    }

    .works-wrapper-box {
        margin-top: 86px;

        @media #{$xxl} {
            margin-top: 56px;
        }

        @media #{$lg} {
            margin-top: 36px;
        }
    }
}

.works-wrapper-3 {
    display: grid;
    gap: 68px 20px;
    grid-template-columns: repeat(2, 1fr);

    @media #{$xxl} {
        gap: 48px 20px;
    }

    @media #{$sm} {
        gap: 38px 20px;
    }

    @media #{$xs} {
        grid-template-columns: repeat(1, 1fr);
    }

    >* {
        .image {
            transform-origin: bottom right;
        }

        &:nth-child(2n) {
            .image {
                transform-origin: bottom left;
            }
        }
    }

    .work-box {
        .thumb {
            &:hover {
                .t-btn {
                    opacity: 1;
                }
            }

            .image {
                overflow: hidden;
                position: relative;
                border-radius: 20px;
                transform: scale(0.9);

                img {
                    transform-origin: center;
                }
            }

            img {
                width: 100%;
                cursor: none;
            }

            .t-btn {
                font-size: 16px;
                font-weight: 400;
                line-height: 30px;
                letter-spacing: -0.02em;
                padding: 10px 20px;
                display: inline-block;
                background-color: white;
                color: var(--black);
                border-radius: 50px;
                position: absolute;
                top: 0;
                left: 0;
                opacity: 0;
                margin: -25px 0 0 -65px;
                transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
                pointer-events: none;
            }
        }

        .content {
            margin-top: 14px;
        }

        .title {
            font-size: 30px;
            font-weight: 310;
            line-height: 1;
            letter-spacing: -0.08em;

            @media #{$lg} {
                font-size: 22px;
            }

            @media #{$sm} {
                font-size: 20px;
            }
        }

        .meta {
            display: flex;
            gap: 5px;
            align-items: center;
            margin-top: 10px;

            span {
                font-size: 14px;
                font-weight: 400;
                line-height: 1;
                display: flex;
                align-items: center;

                &:not(:first-child):before {
                    content: "";
                    width: 10px;
                    height: 1px;
                    background-color: currentColor;
                    display: inline-block;
                    margin-inline-end: 5px;
                }
            }
        }
    }
}

/* approach area style  */
.approach-area {

    .section-header {
        margin-top: 43px;
    }

    .section-title-wrapper {
        display: grid;
        gap: 20px 60px;
        grid-template-columns: 1fr 1225px;
        align-items: flex-end;

        @media #{$xxl} {
            grid-template-columns: 1fr 905px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 675px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 575px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }
    }

    .section-subtitle {
        font-family: var(--font_sequelsansromanbody);
        font-size: 30px;
        font-weight: 310;
        line-height: 1.16;
        letter-spacing: -0.07em;
        color: var(--primary);

        @media #{$xxl} {
            font-size: 24px;
        }

        @media #{$md} {
            font-size: 18px;

            br {
                display: none;
            }
        }
    }

    .section-title {
        max-width: 1126px;

        span {
            color: rgba(17, 17, 17, 0.3);

            @include dark {
                color: rgba(252, 247, 243, 0.3);
            }
        }
    }

    .approach-wrapper-box {
        margin-top: 94px;
        display: grid;
        gap: 20px 60px;
        grid-template-columns: 1fr 1225px;
        align-items: flex-start;
        margin-bottom: 80px;

        @media #{$xxl} {
            grid-template-columns: 1fr 905px;
            margin-top: 64px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 675px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 575px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
            margin-top: 44px;
        }

        .steps {
            font-family: var(--font_sequelsansromanbody);
            font-size: 265px;
            font-weight: 310;
            line-height: 0.65;
            letter-spacing: -0.07em;
            color: var(--primary);

            @media #{$xxl} {
                font-size: 205px;
            }

            @media #{$xl} {
                font-size: 165px;
            }

            @media #{$md} {
                display: none;
            }
        }
    }

    .approach-box {
        display: grid;
        gap: 10px 50px;
        grid-template-columns: 60px 1fr 595px;
        align-items: flex-start;
        padding-top: 24px;
        padding-bottom: 24px;
        border-bottom: 1px dashed #878482;

        &:first-child {
            border-top: 1px dashed #878482;
        }

        @media #{$xxl} {
            grid-template-columns: 60px 1fr 395px;
        }

        @media #{$xl} {
            grid-template-columns: 60px 1fr;
        }

        @media #{$md} {
            gap: 10px 30px;
        }

        @media #{$xs} {
            grid-template-columns: 30px 1fr;
        }

        .number {
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
            color: var(--primary);

            @media #{$xl} {
                grid-row: span 2;
            }
        }

        .title {
            font-size: 30px;
            font-weight: 310;
            line-height: 30px;
            letter-spacing: -0.07em;

            @media #{$xxl} {
                font-size: 24px;
            }
        }

        .text {
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
        }
    }
}

/* funfact area 2 style  */
.funfact-area-2 {
    background-color: var(--bg);

    .section-header {
        margin-top: 50px;
    }

    .section-title {
        max-width: 820px;
        color: #FCF7F3;

        @media #{$xl} {
            max-width: 500px;
        }
    }

    .section-content {

        margin-top: 79px;

        @media #{$xxl} {
            margin-top: 59px;
        }

        .text {
            font-size: 20px;
            font-weight: 400;
            line-height: 28px;
            color: var(--white-2);
            max-width: 410px;
        }

        .year {
            font-family: var(--font_sequelsansromanbody);
            font-size: 580px;
            font-weight: 315;
            line-height: 0.80;
            letter-spacing: -0.1em;
            color: #FCF7F3;
            display: inline-block;

            @media #{$xxl} {
                font-size: 380px;
            }

            @media #{$xl} {
                font-size: 320px;
            }

            @media #{$lg} {
                font-size: 240px;
            }

            @media #{$md} {
                font-size: 150px;
            }

            @media #{$xs} {
                font-size: 100px;
            }
        }

        .info-text {
            margin-top: 49px;
            margin-left: 265px;
            display: grid;
            gap: 20px 90px;
            grid-template-columns: auto 1fr;
            align-items: flex-end;

            @media #{$xxl} {
                margin-top: 29px;
            }

            @media #{$xl} {
                margin-left: 165px;
            }

            @media #{$md} {
                margin-top: 19px;
            }

            @media #{$sm} {
                margin-left: 0;
            }

            @media #{$xs} {
                grid-template-columns: 1fr;
            }

            img {
                height: 43px;

                @media #{$xxl} {
                    height: 33px;
                }

                @media #{$lg} {
                    height: 20px;
                }

                @media #{$xs} {
                    display: none;
                }
            }

            .text {
                font-family: var(--font_sequelsansromanbody);
                font-size: 30px;
                font-weight: 310;
                line-height: 1.16;
                letter-spacing: -0.07em;
                color: #FCF7F3;
                max-width: 273px;
                margin-top: 42px;

                @media #{$xxl} {
                    font-size: 24px;
                    max-width: 223px;
                }

                @media #{$lg} {
                    font-size: 18px;
                    max-width: 173px;
                    margin-top: 22px;
                }
            }
        }

        .text-wrapper {
            margin-left: 265px;

            @media #{$xl} {
                margin-left: 165px;
            }

            @media #{$sm} {
                margin-left: 0;
            }
        }
    }

    .thumb {
        margin-top: 94px;

        img {
            width: 100%;
            transform: scale(0.67);
            transform-origin: top right;
        }
    }
}

/* client area 3 style  */
.client-area-3 {
    background-color: var(--bg);

    .section-header {
        margin-top: 69px;

        .text {
            font-size: 20px;
            font-weight: 400;
            line-height: 28px;
            text-align: center;
            max-width: 340px;
            color: #FCF7F3;
            margin-inline: auto;
        }
    }

    .clients-wrapper-box {
        margin-top: 63px;
        margin-bottom: 80px;

        @media #{$lg} {
            margin-top: 43px;
        }
    }

    .clients-wrapper {
        display: flex;
        gap: 0;
        justify-content: center;
        flex-wrap: wrap;

        .client-slider-active {
            .swiper-slide {
                width: auto;
            }

            .swiper-wrapper {
                transition-timing-function: linear !important;
            }
        }
    }

    .client-box {
        border: 1px solid rgba(252, 247, 243, 0.1);
        border-radius: 70px;
        width: 215px;
        height: 140px;
        padding: 0 20px;
        display: inline-flex;
        justify-content: center;
        align-items: center;

        @media #{$xxl} {
            width: 155px;
            height: 90px;
        }

        @media #{$xl} {
            width: 135px;
            height: 70px;
        }
    }
}

/* blog area style  */
.blog-area {
    .section-title {

        .rr-btn-group {

            &:hover {
                .c {
                    transform: translate(-11px, 0px);
                }
            }
        }
    }

    .section-title {
        max-width: 710px;
    }

    .blogs-wrapper-box {
        margin-top: 86px;

        @media #{$xxl} {
            margin-top: 56px;
        }

        @media #{$lg} {
            margin-top: 36px;
        }
    }

    .blogs-wrapper {
        display: grid;
        gap: 76px 60px;
        grid-template-columns: repeat(4, 1fr);
        overflow: hidden;

        @media #{$xxl} {
            gap: 46px 40px;
        }

        @media #{$lg} {
            grid-template-columns: repeat(2, 1fr);
        }

        @media #{$xs} {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .blog {
        position: relative;

        &:hover {
            .thumb {
                img {
                    transform: scale(1.1);
                }
            }

            .title {
                .arrow {
                    background-color: var(--primary);

                    svg {
                        transform: rotate(60deg);

                        * {
                            fill: var(--white);

                            @include dark {
                                fill: var(--black);
                            }
                        }
                    }
                }
            }
        }

        &:before {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: var(--border);
            top: 0;
            left: -30px;

            @media #{$xxl} {
                left: -20px;
            }
        }

        .thumb {
            overflow: hidden;

            img {
                width: 100%;
                transition: all 0.5s;
            }
        }

        .content {
            margin-top: 24px;

            @media #{$lg} {
                margin-top: 14px;
            }
        }

        .title {
            font-size: 36px;
            font-weight: 310;
            line-height: 1;
            letter-spacing: -0.07em;
            display: inline;

            @media #{$xxl} {
                font-size: 24px;
            }

            @media #{$xl} {
                font-size: 22px;
            }

            @media #{$lg} {
                font-size: 20px;
            }

            .arrow {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 25px;
                height: 25px;
                transition: all 0.3s;
                border-radius: 50%;
                border: 2px solid var(--primary);
                transform: translate(0px, -1px);
                margin-left: 5px;

                @media #{$xxl} {
                    width: 20px;
                    height: 20px;
                }

                @media #{$xl} {
                    width: 17px;
                    height: 17px;
                }

                @media #{$lg} {
                    width: 15px;
                    height: 15px;
                    border-width: 1px;
                }

                svg {
                    transition: all 0.3s;
                    width: 13px;

                    @media #{$xxl} {
                        width: 10px;
                    }

                    @media #{$xl} {
                        width: 7px;
                    }

                    * {
                        fill: var(--primary);
                    }
                }
            }
        }

        .meta {
            display: flex;
            gap: 5px;
            align-items: center;
            margin-top: 14px;

            @media #{$lg} {
                margin-top: 9px;

            }

            span {
                font-size: 14px;
                font-weight: 400;
                line-height: 24px;
                color: var(--secondary);

                &.has-left-line {
                    padding-inline-start: 15px;

                    &:before {
                        width: 10px;
                    }
                }
            }

            .name span {
                font-weight: 500;
                color: var(--primary);
            }
        }
    }
}

/* cta area 3 style  */
.cta-area-3 {
    .section-header {
        margin-top: 50px;
        margin-left: 925px;
        margin-bottom: 86px;

        @media #{$xxl} {
            margin-bottom: 56px;
            margin-left: 555px;
        }

        @media #{$xl} {
            margin-left: 475px;
        }

        @media #{$lg} {
            margin-bottom: 36px;
            margin-left: 325px;
        }

        @media #{$md} {
            margin-left: 0;
        }

        .rr-btn-group {

            &:hover {
                .c {
                    transform: translate(-11px, 0px);
                }
            }
        }
    }

    .section-title {
        max-width: 680px;

        @media #{$xxl} {
            max-width: 540px;
        }

        @media #{$xl} {
            max-width: 410px;
        }

        @media #{$lg} {
            max-width: 340px;

        }
    }
}