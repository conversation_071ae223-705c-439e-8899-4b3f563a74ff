<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Redox HTML Template">

  <title>Redox - Creative Agency and Portfolio HTML Template</title>

  <!-- Fav Icon -->
  <link rel="icon" type="image/x-icon" href="assets/imgs/logo/favicon.png">

  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="assets/vendor/bootstrap.min.css">
  <link rel="stylesheet" href="assets/vendor/fontawesome.min.css">
  <link rel="stylesheet" href="assets/vendor/swiper-bundle.min.css">
  <link rel="stylesheet" href="assets/vendor/meanmenu.min.css">
  <link rel="stylesheet" href="assets/vendor/magnific-popup.css">
  <link rel="stylesheet" href="assets/vendor/animate.min.css">

  <!-- Template Main CSS File -->
  <link rel="stylesheet" href="assets/css/style.css">

</head>

<body class="body-wrapper body-page-inner font-heading-sequelsans-romanbody">

  <!-- Preloader -->
  <div id="preloader">
    <div id="container" class="container-preloader">
      <div class="animation-preloader">
        <div class="spinner"></div>

      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
  </div>

  <!-- Sroll to top -->
  <div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"></path>
    </svg>
  </div>

  <!-- side toggle start -->
  <aside class="fix">
    <div class="side-info">
      <div class="side-info-content">
        <div class="offset-widget offset-header">
          <div class="offset-logo">
            <a href="index.html">
              <img src="assets/imgs/logo/logo-2.png" alt="site logo">
            </a>
          </div>
          <button id="side-info-close" class="side-info-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mobile-menu d-xl-none fix"></div>
        <div class="offset-button">
          <a href="contact.html" class="rr-btn">
            <span class="btn-wrap">
              <span class="text-one">Let's Talk</span>
              <span class="text-two">Let's Talk</span>
            </span>
          </a>
        </div>
        <div class="offset-widget-box">
          <h2 class="title">Contact US</h2>
          <div class="contact-meta">
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-location-dot"></i></span>
              <span class="text">3891 Ranchview Dr. Richardson</span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-envelope"></i></span>
              <span class="text"><a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-phone"></i></span>
              <span class="text"><a href="tel:(505)555-0125">(*************</a></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
  <div class="offcanvas-overlay"></div>
  <!-- side toggle end -->

  <!-- Header area start -->
  <header class="header-area-2">
    <div class="header-main">
      <div class="container large">
        <div class="header-area-2__inner">
          <div class="header__logo">
            <a href="index.html">
              <img src="assets/imgs/logo/logo-2.png" class="normal-logo" alt="Site Logo">
            </a>
          </div>
          <div class="header__nav pos-center">
            <nav class="main-menu">
              <ul>
                <li class="menu-item-has-children">
                  <a href="#">Home</a>
                  <ul class="dp-menu col-2">
                    <li><a href="digital-agency.html">Digital Agency</a></li>
                    <li><a href="creative-agency.html">Creative Agency</a></li>
                    <li><a href="marketing-agency.html">Marketing Agency</a></li>
                    <li><a href="design-agency.html">Design Agency</a></li>
                    <li><a href="startup-agency.html">Startup Agency</a></li>
                    <li><a href="modern-agency.html">Modern Agency</a></li>
                    <li><a href="agency-portfolio.html">Agency Portfolio</a></li>
                    <li><a href="portfolio-horizontal.html">Portfolio Horizontal</a></li>
                    <li><a href="portfolio-line-effect.html">Portfolio Line Effect</a></li>
                    <li><a href="portfolio-box-effect.html">Portfolio Box Effect</a></li>
                    <li><a href="portfolio-vertical.html">Portfolio Vertical</a></li>
                    <li><a href="portfolio-slicer.html">Portfolio Slicer</a></li>
                    <li><a href="parallax-carousal.html">Parallax Carousal</a></li>
                    <li><a href="portfolio-showcase.html">Portfolio Showcase</a></li>
                  </ul>
                </li>
                <li><a href="about.html">About Us</a></li>
                <li class="menu-item-has-children">
                  <a href="#">Service</a>
                  <ul class="dp-menu">
                    <li><a href="services.html">Core Services</a></li>
                    <li><a href="services-2.html">Services ST. Pulse</a></li>
                    <li><a href="services-3.html">Services ST. Morph</a></li>
                    <li><a href="services-4.html">Services ST. Nova</a></li>
                    <li><a href="services-5.html">Services ST. Zenith</a></li>
                    <li><a href="services-6.html">Services ST. Prism</a></li>
                    <li><a href="service-details.html">Service Details</a></li>
                  </ul>
                </li>
                <li class="menu-item-has-children">
                  <a href="#">Blog</a>
                  <ul class="dp-menu">
                    <li><a href="blog.html">Blog</a></li>
                    <li><a href="blog-details.html">Blog Details</a></li>
                  </ul>
                </li>
                <li class="menu-item-has-children">
                  <a href="#">Pages</a>
                  <ul class="dp-menu">
                    <li><a href="portfolio.html">Core Portfolio</a></li>
                    <li><a href="portfolio-2.html">Portfolio ST. Classic</a></li>
                    <li><a href="portfolio-3.html">Portfolio ST. Minimal</a></li>
                    <li><a href="portfolio-4.html">Portfolio ST. Modern</a></li>
                    <li><a href="portfolio-5.html">Portfolio ST. Interactive</a></li>
                    <li><a href="portfolio-6.html">Portfolio ST. Metro</a></li>
                    <li><a href="portfolio-details.html">Portfolio Details</a></li>
                    <li><a href="team.html">Team</a></li>
                    <li><a href="team-details.html">Team Details</a></li>
                    <li><a href="faq.html">FAQ Page</a></li>
                    <li><a href="404.html">404 Page</a></li>
                  </ul>
                </li>
                <li><a href="contact.html">Contact</a></li>
              </ul>
            </nav>
          </div>
          <div class="header__button">
            <a href="contact.html" class="rr-btn">
              <span class="btn-wrap">
                <span class="text-one">Let's Talk</span>
                <span class="text-two">Let's Talk</span>
              </span>
            </a>
          </div>
          <div class="header__navicon">
            <button class="side-toggle"><img src="assets/imgs/icon/icon-2.webp" alt="image"></button>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- Header area end -->

  <div class="has-smooth" id="has_smooth"></div>
  <div id="smooth-wrapper">
    <div id="smooth-content">

      <main>

        <!-- work details area start  -->
        <section class="work-details-area">
          <div class="work-details-area-inner section-spacing">
            <div class="container large">
              <div class="section-header fade-anim">
                <div class="section-title-wrapper">
                  <div class="title-thumb">
                    <img src="assets/imgs/gallery/image-26.webp" alt="image">
                  </div>
                  <div class="title-wrapper">
                    <h2 class="section-title font-sequelsans-romanbody">Saudi <br>
                      Venture Capital</h2>
                  </div>
                </div>
              </div>
              <div class="meta-wrapper fade-anim">
                <div class="meta-item">
                  <p class="title">Service</p>
                  <p class="text">Visual Identity, Branding</p>
                </div>
                <div class="meta-item">
                  <p class="title">Client</p>
                  <p class="text">Softakey Digital Agency</p>
                </div>
                <div class="meta-item">
                  <p class="title">Date</p>
                  <p class="text">January 2025</p>
                </div>
                <div class="meta-item">
                  <p class="title">Technology</p>
                  <p class="text">Figma, WordPress</p>
                </div>
              </div>
            </div>
            <div class="image-wrapper parallax-view fade-anim">
              <img class="w-100" src="assets/imgs/gallery/image-27.webp" alt="image" data-speed="0.8">
            </div>
            <div class="container large">
              <div class="section-info fade-anim">
                <div class="title-wrapper">
                  <h2 class="title">Build streamline and
                    evolve together with
                    solution</h2>
                </div>
                <div class="content">
                  <div class="text-wrapper">
                    <p class="text">Myriam was first trained as a sculptor in Montreal and then in Helsinki, Finland.
                      She is now based in Quebec but works for clients all around the globe. From textile design to
                      murals, editorial illustrations and book covers, her style is recognized by her simple and
                      perfectly arranged shapes as well as her rich and vibrant color palette. Striking pewter studded
                      epaulettes silver zips inner drawstring waist channel</p>
                  </div>
                  <div class="feature-list">
                    <ul>
                      <li>Brand Development</li>
                      <li>UX/UI Design</li>
                      <li>Front-end Development</li>
                      <li>Copywriting</li>
                      <li>Shopify Development</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div class="gallery-wrapper fade-anim">
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-28.webp" alt="image" data-speed="0.8">
              </div>
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-29.webp" alt="image" data-speed="0.8">
              </div>
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-30.webp" alt="image" data-speed="0.8">
              </div>
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-31.webp" alt="image" data-speed="0.8">
              </div>
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-32.webp" alt="image" data-speed="0.8">
              </div>
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-33.webp" alt="image" data-speed="0.8">
              </div>
            </div>
            <div class="container large">
              <div class="section-details fade-anim">
                <div class="details-info">
                  <h3 class="title">Visual Hierarchy</h3>
                  <p class="text">Visual hierarchy is the principle of arranging elements to show their order of
                    importance. information easily. By laying out elements logically designers working process by
                    wireframing.</p>
                </div>
                <div class="details-info">
                  <h3 class="title">Components</h3>
                  <p class="text">From textile design to murals, editorial illustrations and book covers, her style is
                    recognized by her simple and perfectly arranged shapes as well as her rich and vibrant color
                    palette.</p>
                </div>
              </div>
            </div>
            <div class="gallery-wrapper-2 fade-anim">
              <div class="image parallax-view">
                <img src="assets/imgs/gallery/image-34.webp" alt="image" data-speed="0.8">
              </div>
            </div>
          </div>
          <div class="container large">
            <div class="pagination fade-anim">
              <a href="#"><svg width="13" height="18" viewBox="0 0 13 18" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M7.87111 10.8964C7.02203 10.2784 6.09262 9.77888 5.11148 9.40696C4.6913 9.24769 4.26163 9.11181 3.82472 9.00006C3.82464 9.00008 3.82457 9.0001 3.82449 9.00012C5.28169 9.37281 6.65834 10.0138 7.87111 10.8966C9.8992 12.3726 11.3571 14.4421 12.0189 16.7842L12.019 16.7842C11.9496 16.5388 11.8716 16.2965 11.785 16.0575C11.0455 14.015 9.68677 12.2179 7.87111 10.8964ZM12.019 1.21564C12.019 1.2156 12.019 1.21556 12.019 1.21552L10.8974 0.930425C10.3017 3.03841 8.98963 4.90113 7.16402 6.22966C5.3384 7.5582 3.10175 8.27831 0.800956 8.27831L0.800956 8.27843C3.10175 8.27843 5.3384 7.55832 7.16402 6.22979C8.98963 4.90125 10.3017 3.03854 10.8974 0.930547L12.019 1.21564ZM8.35872 10.2937C7.66151 9.78628 6.91367 9.35356 6.12905 9C6.91367 8.64643 7.66151 8.21372 8.35872 7.70624C10.5268 6.12829 12.0853 3.91587 12.7927 1.41215L13 0.678382L10.3311 -4.51585e-07L10.1237 0.733776C9.57372 2.68028 8.36214 4.40024 6.67645 5.62696C4.9907 6.85371 2.92542 7.51863 0.800955 7.51863L-4.58155e-07 7.51863L-3.2865e-07 10.4814L0.800956 10.4814C2.92542 10.4814 4.9907 11.1463 6.67645 12.373C8.36214 13.5997 9.57372 15.3197 10.1237 17.2662L10.3311 18L13 17.3216L12.7927 16.5878C12.0853 14.0841 10.5268 11.8717 8.35872 10.2937Z"
                    fill="#111111" />
                </svg>
                Prev</a>
              <a href="#">Next<svg width="13" height="18" viewBox="0 0 13 18" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M5.12889 10.8964C5.97797 10.2784 6.90738 9.77888 7.88852 9.40696C8.3087 9.24769 8.73837 9.11181 9.17528 9.00006C9.17536 9.00008 9.17543 9.0001 9.17551 9.00012C7.71831 9.37281 6.34166 10.0138 5.12889 10.8966C3.1008 12.3726 1.64286 14.4421 0.981079 16.7842L0.981047 16.7842C1.05037 16.5388 1.12844 16.2965 1.21498 16.0575C1.95453 14.015 3.31323 12.2179 5.12889 10.8964ZM0.981001 1.21564C0.98099 1.2156 0.980979 1.21556 0.980968 1.21552L2.10262 0.930425C2.69825 3.03841 4.01037 4.90113 5.83598 6.22966C7.6616 7.5582 9.89825 8.27831 12.199 8.27831L12.199 8.27843C9.89825 8.27843 7.6616 7.55832 5.83598 6.22979C4.01037 4.90125 2.69825 3.03854 2.10262 0.930547L0.981001 1.21564ZM4.64128 10.2937C5.33849 9.78628 6.08633 9.35356 6.87095 9C6.08633 8.64643 5.33849 8.21372 4.64128 7.70624C2.4732 6.12829 0.914665 3.91587 0.207307 1.41215L-1.96522e-07 0.678382L2.66894 -4.51585e-07L2.87627 0.733776C3.42628 2.68028 4.63786 4.40024 6.32355 5.62696C8.0093 6.85371 10.0746 7.51863 12.199 7.51863L13 7.51863L13 10.4814L12.199 10.4814C10.0746 10.4814 8.0093 11.1463 6.32355 12.373C4.63786 13.5997 3.42628 15.3197 2.87627 17.2662L2.66894 18L-9.24021e-07 17.3216L0.207306 16.5878C0.914663 14.0841 2.4732 11.8717 4.64128 10.2937Z"
                    fill="#111111" />
                </svg>
              </a>
            </div>
          </div>
        </section>
        <!-- work details area end  -->

      </main>

      <!-- footer area start  -->
      <footer class="footer-area-inner-page section-spacing-top">
        <div class="container large">
          <div class="footer-top-inner">
            <div class="footer-logo">
              <a href="index.html"><img src="assets/imgs/logo/logo-2.png" alt="site-logo"></a>
            </div>
            <div class="info-text">
              <div class="text-wrapper">
                <p class="text">Redox is a startup digital agency of design, development and marketing that works
                  friendly with global client</p>
              </div>
              <div class="info-link">
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
          <div class="footer-widget-wrapper-box">
            <div class="footer-widget-wrapper">
              <div class="footer-widget-box newsletter">
                <form action="#" class="subscribe-form">
                  <div class="input-field">
                    <input type="email" placeholder="Enter your email">
                    <button type="submit" class="subscribe-btn"><img src="assets/imgs/icon/icon-1.webp"
                        alt="image"></button>
                  </div>
                </form>
                <div class="subscription-text">
                  <div class="text-wrapper">
                    <p class="text">By subscribing you agree with our
                      <a href="#">Privacy Policy</a>
                    </p>
                  </div>
                </div>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Company</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">agency</a></li>
                  <li><a href="#">Solutions</a></li>
                  <li><a href="#">Community</a></li>
                  <li><a href="#">Work</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Social</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">Facebook</a></li>
                  <li><a href="#">Twitter</a></li>
                  <li><a href="#">Dribbble</a></li>
                  <li><a href="#">Instagram</a></li>
                  <li><a href="#">Awwwards</a></li>
                  <li><a href="#">YouTube</a></li>
                </ul>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Office</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">New York</a></li>
                  <li><a href="#">Toronto</a></li>
                  <li><a href="#">Berlin</a></li>
                  <li><a href="#">London</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="copyright-area">
          <div class="copyright-area-inner">
            <div class="copyright-text">
              <p class="text">© 2025 <a href="https://themeforest.net/user/ravextheme">RavexTheme.</a> All right
                reserved</p>
            </div>
          </div>
        </div>
      </footer>
      <!-- footer area end  -->

    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/jquery-3.7.1.min.js"></script>
  <script src="assets/vendor/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/jquery.magnific-popup.min.js"></script>
  <script src="assets/vendor/swiper-bundle.min.js"></script>
  <script src="assets/vendor/gsap.min.js"></script>
  <script src="assets/vendor/ScrollTrigger.min.js"></script>
  <script src="assets/vendor/ScrollSmoother.min.js"></script>
  <script src="assets/vendor/ScrollToPlugin.min.js"></script>
  <script src="assets/vendor/SplitText.min.js"></script>
  <script src="assets/vendor/TextPlugin.js"></script>
  <script src="assets/vendor/customEase.js"></script>
  <script src="assets/vendor/Flip.min.js"></script>
  <script src="assets/vendor/jquery.meanmenu.min.js"></script>
  <script src="assets/vendor/backToTop.js"></script>
  <script src="assets/vendor/matter.js"></script>
  <script src="assets/vendor/throwable.js"></script>
  <script src="assets/js/magiccursor.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>