<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Form</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .message { margin-top: 15px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Contact Form</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="phone">Phone Number</label>
            <input type="tel" id="phone" name="phone">
        </div>
        
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="subject">Subject *</label>
            <input type="text" id="subject" name="subject" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" rows="4" required></textarea>
        </div>
        
        <input type="hidden" name="csrf_token" id="csrf_token" value="">
        <input type="text" name="website" style="display:none;" tabindex="-1" autocomplete="off">
        
        <button type="submit">Send Message</button>
    </form>
    
    <div id="response"></div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        $(document).ready(function() {
            // Load CSRF token
            $.get('./mail.php?get_csrf=1')
                .done(function(response) {
                    console.log('CSRF response:', response);
                    if (response && response.csrf_token) {
                        $('#csrf_token').val(response.csrf_token);
                        console.log('CSRF token loaded:', response.csrf_token);
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('CSRF load failed:', status, error);
                });
            
            // Handle form submission
            $('#testForm').submit(function(e) {
                e.preventDefault();
                
                console.log('Form data:', $(this).serialize());
                
                $.ajax({
                    type: 'POST',
                    url: './mail.php',
                    data: $(this).serialize(),
                    dataType: 'json'
                })
                .done(function(response) {
                    console.log('Success response:', response);
                    if (response.success) {
                        $('#response').html('<div class="message success">' + response.message + '</div>');
                        $('#testForm')[0].reset();
                    } else {
                        $('#response').html('<div class="message error">' + response.message + '</div>');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Form submission failed:', xhr.status, xhr.responseText);
                    $('#response').html('<div class="message error">Error: ' + xhr.status + ' - ' + xhr.responseText + '</div>');
                });
            });
        });
    </script>
</body>
</html>
