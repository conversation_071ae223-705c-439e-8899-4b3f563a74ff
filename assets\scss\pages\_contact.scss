/* contact page css */

/* Loading effect */
.loading-form {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 99;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    color: var(--white);
    font-size: 20px;
    text-align: center;
    padding-top: 20%;
}

.success-message {
    color: green;
    margin-top: 10px;
}

.error-message {
    color: red;
    margin-top: 10px;
}

#response-message {
    margin-top: 10px;
}


.contact-area-contact-page {

    .section-header {
        border-top: 1px solid var(--border);
        padding-top: 37px;

        @media #{$md} {
            padding-top: 7px;
        }
    }

    .section-title-wrapper {
        display: grid;
        gap: 15px 60px;
        grid-template-columns: 1fr 1235px;

        @media #{$xxl} {
            grid-template-columns: 1fr 1000px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 850px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 750px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }
    }

    .subtitle-wrapper {
        margin-top: 8px;
    }

    .section-title {
        max-width: 875px;

        @media #{$xxl} {
            max-width: 675px;
        }

        @media #{$xl} {
            max-width: 495px;
        }
    }

    .section-content-wrapper {
        margin-top: 90px;
        margin-bottom: 4px;
        display: grid;
        gap: 40px 60px;
        grid-template-columns: 1fr 1030px;
        align-items: flex-start;

        @media #{$xxl} {
            margin-top: 60px;
            grid-template-columns: 1fr 730px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 680px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 550px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }
    }

    .contact-mail {
        .title {
            font-size: 20px;
            font-weight: 400;
            line-height: 20px;
            color: var(--primary);
        }

        .text {
            font-family: var(--font_sequelsansromanbody);
            font-size: 30px;
            font-weight: 310;
            line-height: 1.16;
            letter-spacing: -0.07em;
            color: var(--primary);
            max-width: 410px;
            margin-top: 24px;

            @media #{$xxl} {
                font-size: 24px;
                max-width: 330px;
            }

            @media #{$lg} {
                font-size: 20px;
                max-width: 280px;
            }

            a {
                text-decoration: underline;
                text-decoration-skip-ink: auto;
                text-decoration-thickness: 1px;
                text-underline-offset: 3px;

                &:hover {
                    color: var(--secondary);
                }
            }
        }
    }

    .contact-social {
        margin-top: 51px;

        .title {
            font-size: 20px;
            font-weight: 400;
            line-height: 20px;
            color: var(--primary);
        }

        .social-links {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-top: 24px;

            a {
                font-family: var(--font_sequelsansromanbody);
                font-size: 30px;
                font-weight: 310;
                line-height: 1.16;
                letter-spacing: -0.07em;
                color: var(--primary);
                display: inline-block;
                transition: all 0.5s;

                @media #{$xxl} {
                    font-size: 24px;
                }

                @media #{$lg} {
                    font-size: 20px;
                }

                &:hover {
                    text-decoration: underline;
                    text-decoration-skip-ink: auto;
                    text-decoration-thickness: 1px;
                    text-underline-offset: 3px;
                }
            }
        }

    }


    .contact-formwrap {
        display: grid;
        gap: 60px 60px;
        grid-template-columns: repeat(2, 1fr);

        @media #{$xl} {
            gap: 40px 40px;
        }

        @media #{$lg} {
            gap: 30px 30px;
        }

        @media #{$sm} {
            grid-template-columns: repeat(1, 1fr);
        }

        .message {
            grid-column: span 2;
            margin-top: 30px;

            @media #{$sm} {
                grid-column: auto;
            }
        }
    }

    .contact-formfield {
        input, textarea {
            width: 100%;
            min-height: 40px;
            border: none;
            border-bottom: 1px solid var(--primary);
            outline: none;
            background-color: transparent;
            transition: all 0.5s;
            color: var(--primary);
            resize: vertical;

            &:focus {
                border-color: var(--primary);
            }

            &::placeholder {
                color: var(--primary);
            }

            &:-webkit-autofill,
            &:-webkit-autofill:focus {
                transition: background-color 0s 600000s, color 0s 600000s !important;
            }

            // Error state styling
            &.error {
                border-color: #e74c3c !important;
                box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
            }
        }

        textarea {
            padding: 10px 0;
            font-family: inherit;
            font-size: inherit;
        }

        select {
            width: 100%;
            height: 40px;
            border: none;
            border-bottom: 1px solid var(--primary);
            outline: none;
            background-color: transparent;
            transition: all 0.5s;
            color: var(--primary);

            &:focus {
                border-color: var(--primary);
            }

            &.error {
                border-color: #e74c3c !important;
                box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
            }

            option {
                width: 100%;
                max-width: 100%;

                @include dark {
                    background-color: var(--black);
                }

                &[disabled] {
                    background-color: var(--black) !important;
                }
            }
        }

        // Error message styling
        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: block;
            font-weight: 400;
        }
    }

    .submit-btn {
        margin-top: 50px;

        @media #{$xl} {
            margin-top: 40px;
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }
    }

    // Enhanced response message styling
    #response-message {
        margin-top: 20px;

        .success-message {
            color: #27ae60;
            background-color: rgba(39, 174, 96, 0.1);
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            font-weight: 500;
        }

        .error-message {
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
            font-weight: 500;
        }
    }

    // Enhanced loading form styling
    .loading-form {
        text-align: center;
        margin-top: 20px;
        padding: 20px;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 5px;

        p {
            color: var(--primary);
            font-style: italic;
            margin: 0;
        }
    }

    // Honeypot field (anti-spam)
    input[name="website"] {
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        border: none !important;
        background: none !important;
    }
}