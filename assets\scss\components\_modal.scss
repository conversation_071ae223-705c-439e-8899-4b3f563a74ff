/*----------------------------------------*/
/* modal css  */
/*----------------------------------------*/

.modal {
  &__dialog {
    width: 760px;
    max-width: 100%;
    margin-top: 100px;

    @media #{$md} {
      width: 700px;
      margin-top: 80px;
    }

    @media #{$sm} {
      width: 350px;
    }
  }

  &__content {
    height: 500px;

    @media #{$sm} {
      height: 300px;
    }

    iframe {
      width: 100%;
      height: 100%;
    }
  }

  &__close {
    position: absolute;
    width: 40px;
    height: 40px;
    top: -15px;
    right: -15px;
    z-index: 9;
    border-radius: 50px;
    font-size: 20px;
    color: var(--white);
    background: var(--black);
    transition: all 0.3s;

    &:hover {
      color: var(--primary);
    }
  }

  &__sfluence {
    width: 100%;
    height: 100%;
    padding: 60px;

    @media #{$sm} {
      padding: 20px 10px;
    }

    &-area {
      width: 100vw;
      height: 100vh;
      background: var(--black);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9;
      opacity: 0;
      visibility: hidden;
      transition: all 0.5s;
      transform: scale(0.5);

      &.showed {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
      }

      .close_btn {
        position: absolute;
        width: 60px;
        height: 60px;
        top: 0px;
        right: 20px;
        z-index: 9;
        border-radius: 50px;
        font-size: 30px;
        color: var(--white);
        transition: all 0.3s;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: var(--primary);
        }

        @media #{$sm} {
          right: 0;
        }
      }

      iframe,
      video {
        width: 100%;
        height: 100%;

        @media #{$sm} {
          height: 300px;
          object-fit: cover;
          margin-top: 45%;
        }
      }
    }
  }
}