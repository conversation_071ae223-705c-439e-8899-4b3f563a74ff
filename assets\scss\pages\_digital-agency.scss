/* digital agency page css */
.body-digital-agency {
    background-color: #F8F8F8;
    color: var(--secondary);

    &.dark {
        background-color: #111111;
    }

    .container {
        &.large {
            @media (min-width: 1800px) {
                max-width: 1750px;
            }
        }
    }

    .section-header {
        margin-top: 20px;
    }

    .section-subtitle {
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
        display: inline-block;
        text-transform: uppercase;
        color: var(--primary);
        letter-spacing: 1px;
    }
}

/* hero area style  */
.hero-area {
    background-color: rgba(255, 129, 58, 0.15);
    position: relative;
    z-index: 2;

    @include dark {
        background-color: #171717;
    }

    .hero-content {
        display: grid;
        gap: 40px 100px;
        grid-template-columns: 130px 1fr 560px;
        margin-top: 61px;
        margin-bottom: 220px;

        @media #{$xxl} {
            grid-template-columns: 130px 1fr 490px;
            margin-bottom: 140px;
        }

        @media #{$xl} {
            grid-template-columns: 130px 1fr 460px;
            gap: 40px 80px;
        }

        @media #{$lg} {
            grid-template-columns: 130px 1fr;
            margin-bottom: 0px;
        }

        @media #{$sm} {
            margin-top: 31px;
        }

        @media #{$sm} {
            grid-template-columns: 1fr;
        }
    }

    .section-title {
        font-size: 100px;
        font-weight: 500;
        line-height: .9;
        max-width: 660px;

        @media #{$xxl} {
            font-size: 70px;
        }

        @media #{$xl} {
            font-size: 50px;
            max-width: 340px;
        }

        @media #{$lg} {
            max-width: 590px;
        }

        .title-shape-1 {
            margin-left: 10px;
            margin-right: 10px;
            margin-top: -8px;
            width: 100px;
            display: inline-flex;

            @media #{$xxl} {
                width: 80px;
            }

            @media #{$xl} {
                width: 55px;

            }

            @media #{$lg} {
                width: 45px;
                margin-left: 5px;
                margin-right: 5px;
                margin-top: -2px;
            }
        }
    }

    .feature-box {
        .number {
            font-size: 100px;
            font-weight: 400;
            line-height: .72;
            display: inline-block;
            color: var(--primary);

            @media #{$xxl} {
                font-size: 70px;
            }

            @media #{$xl} {
                font-size: 50px;
            }
        }

        .text {
            font-size: 18px;
            font-weight: 400;
            line-height: 21px;
            color: var(--primary);
            margin-top: 21px;
        }
    }

    .text-wrapper {
        margin-top: 87px;

        @media #{$xxl} {
            margin-top: 37px;
        }

        .text {
            font-size: 22px;
            font-weight: 400;
            line-height: 30px;
            color: var(--primary);
            max-width: 490px;

            @media #{$xl} {
                font-size: 18px;
            }
        }
    }

    .award-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        z-index: 1;

        @media #{$lg} {
            grid-row: span 2;
        }

        @media #{$sm} {
            order: 3;
        }

        &:before {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: var(--border);
            top: 0;
            left: calc(50% - 2px);
            z-index: -1;

            @media #{$sm} {
                display: none;
            }
        }

        &:after {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: var(--border);
            top: 0;
            left: calc(50% + 1px);
            z-index: -1;

            @media #{$sm} {
                display: none;
            }
        }

        .circle-text-wrapper {
            padding-top: 30px;
            padding-bottom: 30px;
            background-color: #F9E6DC;

            @include dark {
                background-color: var(--bg);
            }
        }

        .circle-text {
            width: 130px;
            height: 130px;
            padding: 0;
        }
    }


    .features-wrapper-box {
        padding-bottom: 36px;
        border-bottom: 1px solid var(--border);
    }

    .features-wrapper {
        display: grid;
        gap: 40px 60px;
        grid-template-columns: repeat(2, 230px);
        justify-content: space-between;

        @media #{$xxl} {
            grid-template-columns: repeat(2, 1fr);
        }

        @media #{$xs} {
            grid-template-columns: repeat(1, 100%);
        }
    }

    .section-content {
        margin-top: 10px;

        @media #{$lg} {
            max-width: 600px;
        }
    }

    .big-text {
        font-family: var(--font_thunder);
        font-size: 920px;
        font-weight: 700;
        line-height: 0.477;
        letter-spacing: -0.02em;
        text-transform: uppercase;
        color: var(--theme);
        display: flex;
        align-items: center;
        justify-content: center;

        @media #{$xxl} {
            font-size: 690px;
        }

        @media #{$xl} {
            font-size: 590px;
        }

        @media #{$lg} {
            display: none;
        }
    }
}

video.video-area {
    width: 100%;
}

/* about area style  */
.about-area {
    position: relative;

    .pin-spacer {
        z-index: 1;
    }

    &-inner {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .section-title {
        font-family: var(--font_thunder);
        font-size: 120px;
        font-weight: 700;
        line-height: .83;
        text-transform: uppercase;
        display: inline;
        position: relative;

        @media #{$xl} {
            font-size: 80px;
        }
    }

    .section-content {
        text-align: center;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;

        .shape-1 {
            position: absolute;
            content: "";
            width: 20px;
            height: 20px;
            border-left: 1px solid var(--primary);
            border-top: 1px solid var(--primary);
            top: 0;
            left: 0;
        }

        .shape-2 {
            position: absolute;
            content: "";
            width: 20px;
            height: 20px;
            border-right: 1px solid var(--primary);
            border-top: 1px solid var(--primary);
            top: 0;
            right: 0;
        }

        .shape-3 {
            position: absolute;
            content: "";
            width: 20px;
            height: 20px;
            border-left: 1px solid var(--primary);
            border-bottom: 1px solid var(--primary);
            bottom: 0;
            left: 0;
        }

        .shape-4 {
            position: absolute;
            content: "";
            width: 20px;
            height: 20px;
            border-right: 1px solid var(--primary);
            border-bottom: 1px solid var(--primary);
            bottom: 0;
            right: 0;
        }

        .text {
            font-size: 30px;
            font-weight: 400;
            line-height: 1.26;
            letter-spacing: -0.02em;
            max-width: 950px;
            color: var(--primary);
            margin-inline: auto;

            @media #{$xl} {
                font-size: 24px;
                max-width: 800px;
            }

            @media #{$sm} {
                font-size: 20px;
            }
        }

        .text-wrapper {
            opacity: 0;
            transform: translateY(100px);
            transition: transform 0.5s, opacity 0.5s;

            @media #{$lg} {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-wrapper {
            margin-top: 62px;
            opacity: 0;
            transform: translateY(100px);
            transition: transform 0.5s, opacity 0.5s;

            @media #{$lg} {
                opacity: 1;
                margin-top: 50px;
                transform: translateY(0);
            }
        }
    }

    .section-title-wrapper {
        display: none;

        @media #{$lg} {
            display: block;
            margin-bottom: 20px;
        }
    }
}

/* work area style  */
.work-area {

    &-inner {
        margin-top: 18px;
    }

    .section-header {
        display: grid;
        gap: 15px 20px;
        grid-template-columns: 1fr 2fr 1fr;

        @media #{$sm} {
            grid-template-columns: 1fr;

        }

        .text-wrapper {
            text-align: center;

            @media #{$sm} {
                text-align: start;
            }


            .text {
                font-family: var(--font_dmsans);
                font-size: 18px;
                font-style: italic;
                font-weight: 400;
                line-height: 18px;
                color: var(--primary);
            }
        }

        .total-count {
            text-align: end;

            @media #{$sm} {
                text-align: start;
            }

            .number {
                display: inline-block;
                font-family: var(--font_dmsans);
                font-size: 18px;
                font-weight: 400;
                line-height: 18px;
                color: var(--primary);
            }
        }
    }

    .section-title {
        font-family: var(--font_dmsans);
        font-size: 18px;
        font-weight: 400;
        line-height: 18px;
    }

    .works-wrapper-box {
        margin-top: 67px;

        @media #{$lg} {
            margin-top: 47px;
        }
    }

    .all-btn-wrapper {
        margin-top: 70px;
        text-align: center;

        @media #{$lg} {
            margin-top: 50px;
        }
    }
}

.works-wrapper-1 {
    display: grid;
    gap: 60px 40px;
    grid-template-columns: repeat(2, 1fr);

    @media #{$lg} {
        gap: 40px 30px;
    }

    @media #{$sm} {
        grid-template-columns: repeat(1, 1fr);
    }

    >* {
        .image {
            transform-origin: bottom right;
        }

        &:nth-child(2n) {
            .image {
                transform-origin: bottom left;
            }
        }
    }

    .work-box {
        .thumb {
            &:hover {
                .t-btn {
                    opacity: 1;
                }
            }

            .image {
                overflow: hidden;
                position: relative;
                border-radius: 20px;
                transform: scale(0.9);

                img {
                    transform-origin: center;
                }
            }

            img {
                width: 100%;
                cursor: none;
            }

            .t-btn {
                font-size: 16px;
                font-weight: 400;
                line-height: 30px;
                letter-spacing: -0.02em;
                padding: 10px 20px;
                display: inline-block;
                background-color: white;
                color: var(--black);
                border-radius: 50px;
                position: absolute;
                top: 0;
                left: 0;
                opacity: 0;
                margin: -25px 0 0 -65px;
                transition: opacity 0.3s, transform 0.7s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
                pointer-events: none;
            }
        }

        .content {
            margin-top: 13px;
        }

        .title {
            font-size: 24px;
            font-weight: 500;
            line-height: 1.25;
            letter-spacing: -0.02em;

            @media #{$lg} {
                font-size: 20px;
            }
        }

        .meta {
            display: flex;
            gap: 8px;
            align-items: center;

            span {
                font-size: 14px;
                font-weight: 400;
                line-height: 30px;
                letter-spacing: -0.02em;
                color: #999999;
                display: flex;
                align-items: center;

                &:not(:first-child):before {
                    content: "";
                    width: 4px;
                    height: 4px;
                    background-color: var(--primary);
                    display: inline-block;
                    border-radius: 50%;
                    margin-inline-end: 4px;
                }
            }
        }
    }
}


/* service area style  */
.service-area {

    .services-wrapper-box {
        margin-top: 91px;
        margin-bottom: 20px;

        @media #{$xxl} {
            margin-top: 71px;
        }

        @media #{$xl} {
            margin-top: 61px;
        }

        @media #{$lg} {
            margin-top: 41px;
        }
    }

}

.services-wrapper-1 {
    .service-box {
        border-top: 1px solid var(--border);
        padding-top: 30px;
        padding-bottom: 30px;
        display: grid;
        gap: 20px 30px;
        grid-template-columns: 1fr 410px 545px;
        align-items: flex-start;

        @media #{$lg} {
            grid-template-columns: 1fr 310px 445px;
        }

        @media #{$md} {
            grid-template-columns: 1fr 220px 360px;
        }

        @media #{$sm} {
            grid-template-columns: 1fr 380px;
        }

        @media #{$xs} {
            grid-template-columns: 1fr;
        }

        .count {
            .number {
                font-size: 30px;
                font-weight: 500;
                line-height: 1;
                color: var(--primary);
                display: inline-block;

                @media #{$lg} {
                    font-size: 25px;
                }

                @media #{$md} {
                    font-size: 20px;
                }
            }
        }

        .content {
            .title {
                font-size: 30px;
                font-weight: 500;
                line-height: 1;
                color: var(--primary);

                @media #{$md} {
                    font-size: 24px;
                }

                a:hover {
                    color: var(--secondary);
                }
            }
        }

        .service-list {
            margin-top: 22px;

            li {
                font-size: 18px;
                font-weight: 400;
                line-height: 30px;
                color: var(--primary);

                a:hover {
                    color: var(--secondary);
                }
            }
        }

        .thumb {
            border-radius: 20px;
            overflow: hidden;
            text-align: right;

            @media #{$md} {
                border-radius: 10px;
            }

            @media #{$sm} {
                grid-column: span 2;
            }

            @media #{$xs} {
                grid-column: auto;
            }

            img {
                width: 35%;
                object-fit: cover;
                height: 265px;
                border-radius: 20px;

                @media #{$md} {
                    width: 100%;
                    border-radius: 10px;
                }
            }
        }
    }
}

/* funfact area style  */
.funfact-area {
    background-color: var(--bg);

    .section-title {
        color: var(--white);
    }

    .section-title-wrapper {
        margin-top: 41px;
    }

    .funfact-area-inner {
        display: grid;
        gap: 40px 60px;
        grid-template-columns: 1fr 950px;

        @media #{$xxl} {
            grid-template-columns: 1fr 750px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 650px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 550px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }
    }

    .funfact-wrapper-box {
        padding-left: 30px;
        position: relative;
        z-index: 1;

        @media #{$md} {
            padding-left: 0;
            padding-top: 0;
        }

        .line-1 {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            top: 0;
            left: 0;
            z-index: -1;

            @media #{$md} {
                display: none;
            }
        }

        .line-2 {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            top: 0;
            left: 30px;
            z-index: -1;

            @media #{$md} {
                display: none;
            }
        }

        .line-3 {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            top: 0;
            right: 390px;
            z-index: -1;

            @media #{$lg} {
                right: 320px;
            }

            @media #{$md} {
                display: none;
            }
        }

        .line-4 {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.08);
            top: 0;
            right: 360px;
            z-index: -1;

            @media #{$lg} {
                right: 290px;
            }

            @media #{$md} {
                display: none;
            }
        }
    }

    .funfact-wrapper {
        margin-top: 52px;
        margin-bottom: 43px;
    }

    .funfact-item {
        max-width: 360px;
        opacity: 0.2;

        @media #{$lg} {
            max-width: 290px;
        }


        &:not(:first-child) {
            margin-top: 195px;

            @media #{$xl} {
                margin-top: 155px;
            }

            @media #{$lg} {
                margin-top: 115px;
            }

            @media #{$md} {
                margin-top: 20px;
            }

            @media #{$sm} {
                margin-top: 50px;
            }
        }

        &:nth-child(2n) {
            margin-left: auto;

            @media #{$xs} {
                margin-left: 0;
            }
        }

        .number {
            font-size: 130px;
            font-weight: 500;
            line-height: .7;
            display: inline-block;
            color: var(--white);

            @media #{$xl} {
                font-size: 80px;
            }

            @media #{$lg} {
                font-size: 60px;
            }
        }

        .text {
            margin-top: 34px;
            color: #999999;

            @media #{$xl} {
                margin-top: 24px;
            }
        }
    }
}

/* client area style  */
.client-area {
    .section-title {
        max-width: 1430px;

        span {
            color: var(--theme);
        }
    }

    .section-content {
        margin-top: 21px;

        .text-wrapper {
            max-width: 505px;
            margin-top: 133px;
            margin-left: 545px;

            @media #{$xxl} {
                margin-top: 83px;
            }

            @media #{$xl} {
                margin-top: 63px;
                margin-left: 345px;
            }

            @media #{$md} {
                margin-top: 43px;
                margin-left: auto;
            }

            @media #{$sm} {
                max-width: 100%;
                margin-top: 23px;
            }
        }
    }

    .client-capsule-wrapper {
        position: relative;
        overflow: hidden;
        pointer-events: none;
        margin-top: -200px;
        height: 633px;

        @media #{$xxl} {
            height: 533px;
        }

        @media #{$xl} {
            height: 483px;
        }

        @media #{$md} {
            height: 433px;
        }

        >* {
            position: absolute;
            display: inline-block;
            margin-bottom: 0;
            left: 0;
            top: 0;
            user-select: none;
            pointer-events: auto;
            transition: none;
        }
    }

    .client-box {
        width: 215px;
        height: 100px;
        padding: 10px 20px;
        background-color: var(--primary);
        display: inline-flex;
        justify-content: center;
        align-items: center;
        border-radius: 100px;
        transform: translate(-50%, -50%) rotate(0.00rad);

        @media #{$xxl} {
            width: 165px;
            height: 70px;
        }

        @media #{$xl} {
            width: 135px;
            height: 50px;
        }

        @media #{$md} {
            width: 105px;
            height: 40px;
        }

        @media #{$sm} {
            width: 85px;
            height: 30px;
        }

        img {
            pointer-events: none;
            max-width: 100%;
            max-height: 100%;
        }
    }

    .line {
        border-bottom: 1px solid var(--primary);
    }

    .lines-wrapper {
        display: grid;
        gap: 5px 0;

        @media #{$xl} {
            gap: 3px 0;
        }

        @media #{$sm} {
            gap: 1px 0;
        }
    }
}


/* cta area style  */
.cta-area {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;

    &-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;

        .area-bg {
            position: absolute;
            height: 390px;
            width: 390px;
            background-color: var(--black);
            border-radius: 100%;
            z-index: -1;

            @include dark {
                background-color: var(--theme);
            }

            @media #{$xxl} {
                height: 300px;
                width: 300px;
            }

            @media #{$xl} {
                height: 260px;
                width: 260px;
            }

            @media #{$lg} {
                height: 200px;
                width: 200px;
            }

            @media #{$md} {
                height: 160px;
                width: 160px;
            }
        }
    }

    .section-title {
        font-family: var(--font_thunder);
        font-size: 360px;
        font-weight: 700;
        line-height: 20px;
        text-transform: uppercase;
        color: var(--white);
        display: inline-block;
        font-size: 4vw;

        a:hover {
            color: var(--white);
        }
    }

    .section-content {
        text-align: center;

    }
}

/* productivity area style  */
.productivity-area {

    .section-content {
        margin-top: 148px;
        margin-bottom: 105px;
        text-align: center;

        @media #{$xxl} {
            margin-top: 108px;
            margin-bottom: 75px;
        }

        @media #{$md} {
            margin-top: 58px;
            margin-bottom: 55px;
        }
    }

    .section-title {
        max-width: 1140px;
        margin-inline: auto;
        position: relative;
        z-index: 1;

        @media #{$xxl} {
            max-width: 937px;
        }

        @media #{$xl} {
            max-width: 737px;

        }

        @media #{$lg} {
            max-width: 637px;
        }

        @media #{$md} {
            max-width: 527px;
        }

        span {
            color: #999999;
            display: inline-block;

            @include dark {
                color: #555555;
            }
        }

        .shape-1 {
            &:hover {
                &:before {
                    opacity: 1;
                    visibility: visible;
                }
            }

            &:before {
                content: "";
                width: 150px;
                aspect-ratio: 100/100;
                background-image: url(../imgs/shape/shape-5.webp);
                background-size: contain;
                position: absolute;
                top: -141px;
                right: 144px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.1s;
                display: inline-block;

                @media #{$xxl} {
                    width: 100px;
                }

                @media #{$xl} {
                    width: 90px;
                }

                @media #{$lg} {
                    width: 80px;
                }

                @media #{$md} {
                    top: -91px;
                }
            }
        }

        .shape-2 {
            &:hover {
                &:before {
                    opacity: 1;
                    visibility: visible;
                }
            }

            &:before {
                content: "";
                width: 150px;
                aspect-ratio: 100/100;
                background-image: url(../imgs/shape/shape-4.webp);
                background-size: contain;
                position: absolute;
                top: 21px;
                left: -117px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.1s;
                display: inline-block;

                @media #{$xxl} {
                    width: 100px;
                }

                @media #{$xl} {
                    width: 90px;
                }

                @media #{$lg} {
                    width: 80px;
                }

                @media #{$md} {
                    left: -67px;
                }
            }
        }

        .shape-3 {
            &:hover {
                &:before {
                    opacity: 1;
                    visibility: visible;
                }
            }

            &:before {
                content: "";
                width: 150px;
                aspect-ratio: 100/100;
                background-image: url(../imgs/shape/shape-6.webp);
                background-size: contain;
                position: absolute;
                bottom: -177px;
                left: 523px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.1s;
                display: inline-block;

                @media #{$xxl} {
                    width: 100px;
                    bottom: -137px;
                }

                @media #{$xl} {
                    width: 90px;
                    bottom: -107px;
                }

                @media #{$lg} {
                    width: 80px;
                }

                @media #{$md} {
                    left: 473px;
                    bottom: -57px;
                }
            }
        }
    }

}

/* text slider area style  */
.text-slider-active {
    .swiper-slide {
        width: auto;
    }
}

.text-slider {
    padding-top: 35px;
    padding-bottom: 35px;
    border-bottom: 1px solid var(--border);

    @media #{$lg} {
        padding-top: 25px;
        padding-bottom: 25px;
    }

    @media #{$sm} {
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .swiper-wrapper {
        transition-timing-function: linear !important;
    }
}

.text-slider-box {
    padding-bottom: 3px;
    border-bottom: 1px solid var(--border);
}

.text-slider-item {
    .title {
        font-size: 36px;
        font-weight: 400;
        line-height: 1;
        text-transform: uppercase;
        display: flex;
        align-items: center;

        @media #{$xxl} {
            font-size: 26px;
        }

        @media #{$lg} {
            font-size: 24px;
        }

        @media #{$sm} {
            font-size: 20px;
        }

        .dot {
            width: 10px;
            height: 10px;
            background-color: var(--primary);
            border-radius: 10px;
            margin-inline-end: 35px;
            display: inline-block;
        }
    }
}