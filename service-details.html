<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Redox HTML Template">

  <title>Redox - Creative Agency and Portfolio HTML Template</title>

  <!-- Fav Icon -->
  <link rel="icon" type="image/x-icon" href="assets/imgs/logo/favicon.png">

  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="assets/vendor/bootstrap.min.css">
  <link rel="stylesheet" href="assets/vendor/fontawesome.min.css">
  <link rel="stylesheet" href="assets/vendor/swiper-bundle.min.css">
  <link rel="stylesheet" href="assets/vendor/meanmenu.min.css">
  <link rel="stylesheet" href="assets/vendor/magnific-popup.css">
  <link rel="stylesheet" href="assets/vendor/animate.min.css">

  <!-- Template Main CSS File -->
  <link rel="stylesheet" href="assets/css/style.css">

</head>


<body class="body-wrapper body-page-inner font-heading-sequelsans-romanbody">

  <!-- Preloader -->
  <div id="preloader">
    <div id="container" class="container-preloader">
      <div class="animation-preloader">
        <div class="spinner"></div>

      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
  </div>

  <!-- Sroll to top -->
  <div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"></path>
    </svg>
  </div>

  <!-- side toggle start -->
  <aside class="fix">
    <div class="side-info">
      <div class="side-info-content">
        <div class="offset-widget offset-header">
          <div class="offset-logo">
            <a href="index.html">
              <img src="assets/imgs/logo/logo-2.png" alt="site logo">
            </a>
          </div>
          <button id="side-info-close" class="side-info-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mobile-menu d-xl-none fix"></div>
        <div class="offset-button">
          <a href="contact.html" class="rr-btn">
            <span class="btn-wrap">
              <span class="text-one">Let's Talk</span>
              <span class="text-two">Let's Talk</span>
            </span>
          </a>
        </div>
        <div class="offset-widget-box">
          <h2 class="title">Contact US</h2>
          <div class="contact-meta">
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-location-dot"></i></span>
              <span class="text">3891 Ranchview Dr. Richardson</span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-envelope"></i></span>
              <span class="text"><a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-phone"></i></span>
              <span class="text"><a href="tel:(505)555-0125">(*************</a></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
  <div class="offcanvas-overlay"></div>
  <!-- side toggle end -->

  <!-- Header area start -->
  <header class="header-area-2">
    <div class="header-main">
      <div class="container large">
        <div class="header-area-2__inner">
          <div class="header__logo">
            <a href="index.html">
              <img src="assets/imgs/logo/logo-2.png" class="normal-logo" alt="Site Logo">
            </a>
          </div>
          <div class="header__nav pos-center">
            <nav class="main-menu">
              <ul>
                <li class="menu-item-has-children">
                  <a href="#">Home</a>
                  <ul class="dp-menu col-2">
                    <li><a href="digital-agency.html">Digital Agency</a></li>
                    <li><a href="creative-agency.html">Creative Agency</a></li>
                    <li><a href="marketing-agency.html">Marketing Agency</a></li>
                    <li><a href="design-agency.html">Design Agency</a></li>
                    <li><a href="startup-agency.html">Startup Agency</a></li>
                    <li><a href="modern-agency.html">Modern Agency</a></li>
                    <li><a href="agency-portfolio.html">Agency Portfolio</a></li>
                    <li><a href="portfolio-horizontal.html">Portfolio Horizontal</a></li>
                    <li><a href="portfolio-line-effect.html">Portfolio Line Effect</a></li>
                    <li><a href="portfolio-box-effect.html">Portfolio Box Effect</a></li>
                    <li><a href="portfolio-vertical.html">Portfolio Vertical</a></li>
                    <li><a href="portfolio-slicer.html">Portfolio Slicer</a></li>
                    <li><a href="parallax-carousal.html">Parallax Carousal</a></li>
                    <li><a href="portfolio-showcase.html">Portfolio Showcase</a></li>
                  </ul>
                </li>
                <li><a href="about.html">About Us</a></li>
                <li class="menu-item-has-children">
                  <a href="#">Service</a>
                  <ul class="dp-menu">
                    <li><a href="services.html">Core Services</a></li>
                    <li><a href="services-2.html">Services ST. Pulse</a></li>
                    <li><a href="services-3.html">Services ST. Morph</a></li>
                    <li><a href="services-4.html">Services ST. Nova</a></li>
                    <li><a href="services-5.html">Services ST. Zenith</a></li>
                    <li><a href="services-6.html">Services ST. Prism</a></li>
                    <li><a href="service-details.html">Service Details</a></li>
                  </ul>
                </li>
                <li class="menu-item-has-children">
                  <a href="#">Blog</a>
                  <ul class="dp-menu">
                    <li><a href="blog.html">Blog</a></li>
                    <li><a href="blog-details.html">Blog Details</a></li>
                  </ul>
                </li>
                <li class="menu-item-has-children">
                  <a href="#">Pages</a>
                  <ul class="dp-menu">
                    <li><a href="portfolio.html">Core Portfolio</a></li>
                    <li><a href="portfolio-2.html">Portfolio ST. Classic</a></li>
                    <li><a href="portfolio-3.html">Portfolio ST. Minimal</a></li>
                    <li><a href="portfolio-4.html">Portfolio ST. Modern</a></li>
                    <li><a href="portfolio-5.html">Portfolio ST. Interactive</a></li>
                    <li><a href="portfolio-6.html">Portfolio ST. Metro</a></li>
                    <li><a href="portfolio-details.html">Portfolio Details</a></li>
                    <li><a href="team.html">Team</a></li>
                    <li><a href="team-details.html">Team Details</a></li>
                    <li><a href="faq.html">FAQ Page</a></li>
                    <li><a href="404.html">404 Page</a></li>
                  </ul>
                </li>
                <li><a href="contact.html">Contact</a></li>
              </ul>
            </nav>
          </div>
          <div class="header__button">
            <a href="contact.html" class="rr-btn">
              <span class="btn-wrap">
                <span class="text-one">Let's Talk</span>
                <span class="text-two">Let's Talk</span>
              </span>
            </a>
          </div>
          <div class="header__navicon">
            <button class="side-toggle"><img src="assets/imgs/icon/icon-2.webp" alt="image"></button>
          </div>
        </div>
      </div>
    </div>
  </header>
  <!-- Header area end -->

  <div class="has-smooth" id="has_smooth"></div>
  <div id="smooth-wrapper">
    <div id="smooth-content">

      <main>

        <!-- hero area start  -->
        <section class="hero-area-service-details">
          <div class="container large">
            <div class="hero-area-service-details-inner section-spacing-top">
              <div class="service-meta fade-anim">
                <span class="serial">[SL: 005]</span>
                <span class="tag">[Brand Guideline]</span>
                <span class="next-item"><a href="service-details.html">[Next]</a></span>
              </div>
              <div class="section-header fade-anim">
                <div class="section-title-wrapper">
                  <div class="title-wrapper">
                    <h2 class="section-title font-sequelsans-romanbody">Brand <br>
                      Guideline</h2>
                  </div>
                </div>
              </div>
              <div class="section-content-wrapper fade-anim">
                <div class="section-content">
                  <div class="text-wrapper">
                    <p class="text">You'll need to provide your brand information, and starting history to visualize
                      identity. We speak fluent branding, as we apply a solid base understanding to everything we do
                      in thought and action.</p>
                  </div>
                  <div class="feature-list">
                    <ul>
                      <li>Strategy</li>
                      <li>Brand Identity</li>
                      <li>Communication</li>
                      <li>Research</li>
                      <li>Consultation</li>
                    </ul>
                  </div>
                </div>
                <div class="section-thumb parallax-view">
                  <img src="assets/imgs/gallery/image-24.webp" alt="image" data-speed="0.8">
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- hero area end  -->

        <!-- approach area start  -->
        <section class="approach-area-service-details-page">
          <div class="container large">
            <div class="approach-area-service-details-page-inner section-spacing">
              <div class="section-header">
                <div class="section-title-wrapper">
                  <div class="subtitle-wrapper fade-anim" data-direction="left">
                    <span class="section-subtitle">Our comprehensive <br>
                      design process</span>
                  </div>
                  <div class="title-wrapper fade-anim" data-direction="right">
                    <h2 class="section-title font-sequelsans-romanbody">Whether you’re a startup or industry star,
                      we’re here to promote your brand by
                      creative research and real human centred
                      design.</h2>
                  </div>
                </div>
              </div>
              <div class="approach-wrapper-box">
                <span class="steps fade-anim">04</span>
                <div class="approach-wrapper fade-anim" data-direction="right">
                  <div class="approach-box">
                    <span class="number">01</span>
                    <h3 class="title">Discovery & Research</h3>
                    <p class="text">Our mission is to empower the brands we believe in with tailor-made approaches
                      that ignite creativity and growth without limits.</p>
                  </div>
                  <div class="approach-box">
                    <span class="number">02</span>
                    <h3 class="title">Wireframing & Prototyping</h3>
                    <p class="text">Our mission is to empower the brands we believe in with tailor-made approaches
                      that ignite creativity and growth without limits.</p>
                  </div>
                  <div class="approach-box">
                    <span class="number">03</span>
                    <h3 class="title">Design System</h3>
                    <p class="text">Our mission is to empower the brands we believe in with tailor-made approaches
                      that ignite creativity and growth without limits.</p>
                  </div>
                  <div class="approach-box">
                    <span class="number">04</span>
                    <h3 class="title">Design Validation</h3>
                    <p class="text">Our mission is to empower the brands we believe in with tailor-made approaches
                      that ignite creativity and growth without limits.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- approach area end  -->

        <!-- feature area start  -->
        <section class="feature-area">
          <div class="container large">
            <div class="feature-area-inner section-spacing-top">
              <div class="features-wrapper-box fade-anim">
                <div class="features-wrapper">
                  <div class="feature-box">
                    <div class="thumb">
                      <img src="assets/imgs/shape/shape-15.webp" alt="image">
                    </div>
                    <div class="content">
                      <h3 class="title">Skilled <br>
                        design team</h3>
                      <p class="text">We work closely with your team to understand your mission, values, and goals,
                        forming the foundation of your brand identity.</p>
                    </div>
                  </div>
                  <div class="feature-box">
                    <div class="thumb">
                      <img src="assets/imgs/shape/shape-16.webp" alt="image">
                    </div>
                    <div class="content">
                      <h3 class="title">User-centric <br>
                        design</h3>
                      <p class="text">We bring extensive experience across various industries, delivering tailored
                        design solutions that meet specific sector needs.</p>
                    </div>
                  </div>
                  <div class="feature-box">
                    <div class="thumb">
                      <img src="assets/imgs/shape/shape-17.webp" alt="image">
                    </div>
                    <div class="content">
                      <h3 class="title">Data-driven <br>
                        approach</h3>
                      <p class="text">Our designs are guided by data and user insights, ensuring optimal usability and
                        impactful user experiences.</p>
                    </div>
                  </div>
                  <div class="feature-box">
                    <div class="thumb">
                      <img src="assets/imgs/shape/shape-18.webp" alt="image">
                    </div>
                    <div class="content">
                      <h3 class="title">Collaborative <br>
                        process</h3>
                      <p class="text">We work closely with you throughout the design journey, incorporating your
                        feedback to create designs that align with your vision.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- feature area end  -->

        <!-- value area start  -->
        <section class="value-area">
          <div class="container large">
            <div class="value-area-inner section-spacing">
              <div class="section-content-wrapper fade-anim">
                <div class="section-thumb parallax-view">
                  <img src="assets/imgs/gallery/image-25.webp" alt="image" data-speed="0.8">
                </div>
                <div class="section-content">
                  <div class="section-title-wrapper">
                    <div class="title-wrapper">
                      <h2 class="section-title font-sequelsans-romanbody">We sharpen your brands and
                        businesses create exceptional
                        experiences where people live
                        work </h2>
                    </div>
                  </div>
                  <div class="values-wrapper">
                    <div class="value-box">
                      <h3 class="number">2750</h3>
                      <p class="text">A website refresh or redesign is a comprehensive overhaul that includes
                        substantial changes to the content, structure, visuals, and code of your current website.</p>
                    </div>
                    <div class="value-box">
                      <h3 class="number">92%</h3>
                      <p class="text">High-quality custom logo design for Melbourne businesses. We are here to support
                        you. Description - Our logo design package uniquely blends creative skills and strategic
                        thinking. We don't just create brand identities.</p>
                    </div>
                    <div class="value-box">
                      <h3 class="number">75%</h3>
                      <p class="text">Every creative design begins with a clear objective. Whether it's branding,
                        advertising, product design and user experience, the design must align with the intended
                        purpose to effectively communicate its beyond beauty.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- value area end  -->

        <!-- faq area start  -->
        <section class="faq-area">
          <div class="container large">
            <div class="faq-area-inner section-spacing-top">
              <div class="section-header fade-anim">
                <div class="section-title-wrapper">
                  <div class="subtitle-wrapper">
                    <span class="section-subtitle">FAQ</span>
                  </div>
                  <div class="title-wrapper">
                    <h2 class="section-title font-sequelsans-romanbody">Learn some common
                      answers about newly
                      projects</h2>
                  </div>
                </div>
              </div>
              <div class="accordion-wrapper fade-anim">
                <div class="accordion" id="accordionExample">
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                        Bring their individual experience and creative?
                      </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo">
                        Design should enrich our day?
                      </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                        Human centered design to challenges design theory?
                      </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                        Align with your brand look and feel?
                      </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                        How to become an Agile productive manager?
                      </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseSix" aria-expanded="false" aria-controls="collapseSix">
                        Why we create the best Webflow websites in Figma?
                      </button>
                    </h2>
                    <div id="collapseSix" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <h2 class="accordion-header">
                      <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseSeven" aria-expanded="false" aria-controls="collapseSeven">
                        How to manage Agile project teams?
                      </button>
                    </h2>
                    <div id="collapseSeven" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                      <div class="accordion-body">
                        People know what an FAQ is, so make that your page title. Don’t overcomplicate things by
                        calling it “Good to Know” or “More Info”. Sometimes people put the frequently asked questions
                        section on their Contact page, but you can create your own page and put it right in your
                        website navigation menu
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- faq area end  -->

      </main>

      <!-- footer area start  -->
      <footer class="footer-area-inner-page section-spacing-top">
        <div class="container large">
          <div class="footer-top-inner">
            <div class="footer-logo">
              <a href="index.html"><img src="assets/imgs/logo/logo-2.png" alt="site-logo"></a>
            </div>
            <div class="info-text">
              <div class="text-wrapper">
                <p class="text">Redox is a startup digital agency of design, development and marketing that works
                  friendly with global client</p>
              </div>
              <div class="info-link">
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
            </div>
          </div>
          <div class="footer-widget-wrapper-box">
            <div class="footer-widget-wrapper">
              <div class="footer-widget-box newsletter">
                <form action="#" class="subscribe-form">
                  <div class="input-field">
                    <input type="email" placeholder="Enter your email">
                    <button type="submit" class="subscribe-btn"><img src="assets/imgs/icon/icon-1.webp"
                        alt="image"></button>
                  </div>
                </form>
                <div class="subscription-text">
                  <div class="text-wrapper">
                    <p class="text">By subscribing you agree with our
                      <a href="#">Privacy Policy</a>
                    </p>
                  </div>
                </div>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Company</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">agency</a></li>
                  <li><a href="#">Solutions</a></li>
                  <li><a href="#">Community</a></li>
                  <li><a href="#">Work</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Social</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">Facebook</a></li>
                  <li><a href="#">Twitter</a></li>
                  <li><a href="#">Dribbble</a></li>
                  <li><a href="#">Instagram</a></li>
                  <li><a href="#">Awwwards</a></li>
                  <li><a href="#">YouTube</a></li>
                </ul>
              </div>
              <div class="footer-widget-box">
                <h2 class="title">Office</h2>
                <ul class="footer-nav-list">
                  <li><a href="#">New York</a></li>
                  <li><a href="#">Toronto</a></li>
                  <li><a href="#">Berlin</a></li>
                  <li><a href="#">London</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="copyright-area">
          <div class="copyright-area-inner">
            <div class="copyright-text">
              <p class="text">© 2025 <a href="https://themeforest.net/user/ravextheme">RavexTheme.</a> All right
                reserved</p>
            </div>
          </div>
        </div>
      </footer>
      <!-- footer area end  -->

    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/jquery-3.7.1.min.js"></script>
  <script src="assets/vendor/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/jquery.magnific-popup.min.js"></script>
  <script src="assets/vendor/swiper-bundle.min.js"></script>
  <script src="assets/vendor/gsap.min.js"></script>
  <script src="assets/vendor/ScrollTrigger.min.js"></script>
  <script src="assets/vendor/ScrollSmoother.min.js"></script>
  <script src="assets/vendor/ScrollToPlugin.min.js"></script>
  <script src="assets/vendor/SplitText.min.js"></script>
  <script src="assets/vendor/TextPlugin.js"></script>
  <script src="assets/vendor/customEase.js"></script>
  <script src="assets/vendor/Flip.min.js"></script>
  <script src="assets/vendor/jquery.meanmenu.min.js"></script>
  <script src="assets/vendor/backToTop.js"></script>
  <script src="assets/vendor/matter.js"></script>
  <script src="assets/vendor/throwable.js"></script>
  <script src="assets/js/magiccursor.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>