@import "mixin";

/*----------------------------------------*/
/* global css */
/*----------------------------------------*/
// Body Overlay

.body-overlay {
  position: fixed;
  z-index: 9;
  pointer-events: none;
  top: 0;
  opacity: 1;
  inset-inline-start: 0;
  width: 100vw;
  height: 100vh;
  background-repeat: repeat;
  background-position: top left;
  background-image: url(../imgs/writer/body-bg.webp);
}

.container-xl {
  max-width: 1550px;
}

.rr-container-1405 {
  max-width: 1405px;
}

.text-slider-active {
  .swiper-slide {
    width: auto;
  }
}

// scrollbar 
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: #d6d6d6;
}

::-webkit-scrollbar-thumb {
  background: #888;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.pos-abs {
  position: absolute;
}

.circle-text {
  width: 140px;
  height: 140px;
  position: relative;
  border-radius: 100px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px;

  @media #{$md} {
    width: 120px;
    height: 120px;
  }

  .text {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: var(--primary);
    position: absolute;
    -webkit-animation: textRotation 8s linear infinite;
    animation: textRotation 8s linear infinite;
  }

  @-webkit-keyframes textRotation {
    to {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }

  @keyframes textRotation {
    to {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }

  .text span {
    left: 50%;
    top: 0px;
    font-size: 14px;
    text-transform: uppercase;
    position: absolute;
    transform-origin: 0 65px;
  }

  .icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  &:before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    border: 37px solid transparent;
    border-radius: 50%;
  }
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.fix {
  overflow: hidden;
}

.bg-full {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.border-top-bottom {
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
}

.has-top-line {
  position: relative;
  padding-top: 10px;

  &:before {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    background-color: currentColor;
    top: 0;
    left: 0;
  }
}

.has-bottom-line {
  position: relative;
  padding-bottom: 10px;

  &:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    background-color: currentColor;
    bottom: 0;
    left: 0;
  }
}

.has-left-line {
  position: relative;
  padding-inline-start: 35px;
  display: inline-block;

  &:before {
    position: absolute;
    content: "";
    width: 30px;
    height: 1px;
    background-color: currentColor;
    inset-inline-start: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.has-right-line {
  position: relative;
  padding-inline-end: 35px;
  display: inline-block;

  &:after {
    position: absolute;
    content: "";
    width: 30px;
    height: 1px;
    background-color: currentColor;
    inset-inline-end: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.t-btn-play {
  width: 56px;
  height: 56px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1.5px solid currentColor;
  color: var(--primary);
  border-radius: 50%;
  transition: all .5s;
  font-size: 14px;

  @media #{$sm} {
    width: 50px;
    height: 50px;
    font-size: 12px;
    border-width: 1px;
  }

  &:hover {
    color: var(--theme, --action);
  }

  &.light {
    color: var(--white);

    &:hover {
      color: var(--white);
    }
  }

  &.dark {
    color: var(--black);

    &:hover {
      color: var(--black);
    }
  }
}

.show-light {
  display: inline-block;
}

.show-dark {
  display: none;
}

.line-area {
  position: relative;
}

.lines {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;

  .line {
    width: 1px;
    height: 100%;
    background-color: #00515308;
    display: inline-block;
    position: relative;
    z-index: 1;
  }
}

.t-btn-icon {
  i {
    transform: rotate(-45deg);
    transition: all .3s;
    font-size: 20px;
    color: var(--primary);

  }

  &:hover {
    i {
      transform: rotate(0);
    }
  }

}

.list-check {
  li {
    position: relative;
    padding-inline-start: 30px;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.4;
    margin-bottom: 10px;

    &::before {
      content: "";
      position: absolute;
      inset-inline-start: 0;
      background-image: url('../imgs/electrician/check-mark.webp');
      background-repeat: no-repeat;
      width: 14px;
      height: 14px;
      top: 4px;
      transform: rotateY(0deg);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.list-plus {
  li {
    position: relative;
    padding-inline-start: 30px;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.4;
    display: inline-block;
    width: 100%;

    &::before {
      content: "+";
      position: absolute;
      inset-inline-start: 0;
      top: -10px;
      font-size: 30px;
      font-weight: 300;
      line-height: 1;
    }

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

.pos-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.pagination-with-dash {
  font-size: 14px;
  display: flex;
  gap: 10px;
  color: var(--primary);
  align-items: center;

  .dash {
    width: 100px;
    height: 1px;
    background-color: var(--primary);
  }

  .swiper-pagination-current {
    display: flex;
    gap: 5px;
    align-items: center;

    &:before {
      content: url(../imgs/icon/arrow-left.webp);
    }
  }

  .swiper-pagination-total {
    display: flex;
    gap: 5px;
    align-items: center;

    &:after {
      content: url(../imgs/icon/arrow-right.webp);
    }
  }
}

.t__toggle_switcher {
  .slide-toggle-wrapper {
    display: flex;
    justify-content: center;
  }

  .slide-toggle-btn {
    --switcher-width: 40px;
    --switcher-border-width: 2px;
    --switcher-indicator-width: 16px;
    background-color: #F0F7F8;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    user-select: none;

    input {
      display: none;
    }
  }

  .before_label,
  .after_label {
    cursor: pointer;
    font-size: 18px;
    color: var(--primary);
  }

  .toggle-pane {
    display: none;

    &.show {
      display: block;
    }
  }

  &.style-1 {
    .switcher {
      display: inline-block;
      width: var(--switcher-width);
      height: 20px;
      background-color: #999999;
      border: var(--switcher-border-width) solid #999999;
      border-radius: 10px;
      position: relative;
      cursor: pointer;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: var(--switcher-indicator-width);
        height: var(--switcher-indicator-width);
        background-color: #ffffff;
        border-radius: 50%;
        transition: transform 0.3s;
        transform: translate(0px, -50%);
      }
    }

    input:checked+.switcher::before {
      transform: translate(calc(var(--switcher-width) - (var(--switcher-indicator-width) + 2 * var(--switcher-border-width))), -50%);
    }

    input:checked+.switcher {
      background-color: #000;
      border-color: #000;
    }
  }

  &.style-2 {

    .before_label,
    .after_label {
      padding: 22px 38px;
      position: relative;
      z-index: 2;
      font-size: 16px;
      line-height: 1;
      color: var(--primary);

      &:after {
        content: '';
        width: 100%;
        height: 100%;
        border-radius: 60px;
        position: absolute;
        left: 0;
        top: 0;
        background-color: var(--theme);
        z-index: -1;
        opacity: 0;
        visibility: hidden;
        transition: transform 0.3s;
      }

      &.active {
        color: var(--primary);
      }

      &.active:after {
        opacity: 1;
        visibility: visible;
        transform: translatex(0);
      }
    }

    .before_label:after {
      transform: translatex(100%);
    }

    .after_label:after {
      transform: translatex(-100%);
    }

    .slide-toggle-btn {
      gap: 0;
      border-radius: 60px;
    }
  }
}

.parallax-view {
  overflow: hidden;
}

.hover-reveal {
  position: relative;
  overflow: hidden;
  cursor: none;

  &:hover {
    >*:first-child {
      opacity: 1 !important;
    }
  }

  >*:first-child {
    opacity: 0;
    z-index: 1;
  }
}

.section-spacing {
  padding-top: 100px;
  padding-bottom: 100px;

  @media #{$xxl} {
    padding-top: 90px;
    padding-bottom: 90px;
  }

  @media #{$xl} {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  @media #{$lg} {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.section-spacing-top {
  padding-top: 100px;

  @media #{$xxl} {
    padding-top: 90px;
  }

  @media #{$xl} {
    padding-top: 80px;
  }

  @media #{$lg} {
    padding-top: 60px;
  }
}

.section-spacing-bottom {
  padding-bottom: 100px;

  @media #{$xxl} {
    padding-bottom: 90px;
  }

  @media #{$xl} {
    padding-bottom: 80px;
  }

  @media #{$lg} {
    padding-bottom: 60px;
  }
}


.container {
  --bs-gutter-x: 30px;

  &.full-hd {
    @media (min-width: 1600px) {
      max-width: 1920px;
      margin-left: auto;
      margin-right: auto;
      padding-left: 0;
      padding-right: 0;
    }
  }
}

.section-subtitle {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--primary);
}

.section-title {
  font-size: 100px;

  @media #{$xxl} {
    font-size: 80px;
  }

  @media #{$xl} {
    font-size: 60px;
  }

  @media #{$lg} {
    font-size: 50px;
  }

  @media #{$md} {
    font-size: 40px;
  }

  @media #{$sm} {
    font-size: 35px;
  }

  &.font-instrumentsans-medium {
    font-family: var(--font_instrumentsans);
    font-weight: 500;
    line-height: 0.9;
  }

  &.font-sequelsans-romanbody {
    font-family: var(--font_sequelsansromanbody);
    font-weight: 310;
    line-height: 0.9;
    letter-spacing: -0.07em;

    @media #{$md} {
      line-height: 1.1;
    }
  }

  &.font-thunder-regular {
    font-family: var(--font_thunder);
    font-weight: 400;
    line-height: 0.85;
    text-transform: uppercase;

    span {
      font-family: var(--font_timesnow);
      color: rgba(17, 17, 17, 0.4);
      display: inline-block;
      line-height: 0;
    }
  }

  &.font-bdogrotesk-regular {
    font-family: var(--font_bdogrotesk);
    font-weight: 400;
    line-height: 0.95;
    letter-spacing: -0.05em;

    @media #{$md} {
      line-height: 1.1;
    }
  }

  &.font-tartuffotrial-thin {
    font-family: var(--font_tartuffotrial);
    font-weight: 100;
    line-height: 1;
  }
}

/* inverted text style  */
.text-invert>div {
  background-image: linear-gradient(to right,
      var(--primary) 50%,
      #CDC9C6 51%);
  background-size: 200% 100%;
  background-position-x: 100%;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

// side info style
.offcanvas-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #000;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
}

.offcanvas-overlay.overlay-open {
  opacity: 0.5;
  visibility: visible;
}

.side-info-close {
  font-size: 18px;
  padding: 0;
  transition: all 0.3s linear;
  background-color: var(--white);
  color: var(--black);
  width: 40px;
  height: 40px;
  border: 1px solid var(--black);
  border-radius: 50%;
  line-height: 38px;

  @include dark {
    background-color: #292828;
    color: var(--white);
  }

  &:hover {
    transform: rotate(90deg);
  }
}

.side-info {
  background: var(--white) none repeat scroll 0 0;
  padding: 40px 45px;
  position: fixed;
  right: 0;
  top: 0;
  width: 500px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 9999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;

  @include dark {
    background-color: var(--black);
  }

  ::-webkit-scrollbar {
    display: none;
  }

  @media #{$xs} {
    width: 100%;
    padding: 30px 30px;
  }

  &.info-open {
    opacity: 1;
    transform: (translateX(0));
  }
}

.offset {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-logo {
    width: 120px;

    @media #{$xs} {
      width: 100px;
    }

    img {
      max-width: 120px;
    }

    .light-logo {
      @include dark {
        display: none;
      }
    }

    .dark-logo {
      display: none;

      @include dark {
        display: block;
      }
    }
  }

  &-button {
    margin-top: 40px;

    @media (min-width: 576px) {
      display: none;
    }
  }
}

.offset-widget-box {
  margin-top: 40px;

  .title {
    font-size: 24px;
    line-height: 1.33;
    margin-bottom: 15px;

    @media #{$xxl} {
      margin-bottom: 22px;
    }
  }

  .contact-meta>*:not(:first-child) {
    margin-top: 16px;
  }

  .contact-item {
    display: flex;
    align-items: center;
    gap: 14px;

    span {
      color: var(--primary);
      font-weight: 500;

      a {
        &:hover {
          color: var(--secondary);
        }
      }
    }

    .icon {
      width: 40px;
      min-width: 40px;
      height: 40px;
      display: inline-flex;
      border: 1px solid var(--primary);
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: var(--primary);
    }
  }
}

/* mean menu customize */
.mobile-menu {
  margin-top: 40px;

  &.mean-container {

    .mean-nav>ul {
      padding: 0;
      margin: 0;
      width: 100%;
      list-style-type: none;
      display: block !important;

      >li:last-child>a {
        border-bottom: 1px solid var(--border);
      }
    }

    .mean-nav {
      background: none;
      margin-top: 0;

      .new {
        font-size: 10px;
        font-weight: 600;
        background: #FFA38E;
        color: var(--black-2);
        padding: 3px 7px;
        line-height: 1;
        display: flex;
        align-items: center;
        border-radius: 2px;
      }

      ul li {
        a {
          width: 100%;
          padding: 15px 0;
          padding-inline-start: 0px;
          font-weight: 500;
          font-size: 18px;
          line-height: 1;
          color: var(--primary);
          text-transform: capitalize;
          border-top: 1px solid var(--border);
          display: flex;
          gap: 8px;
          justify-content: flex-start;
          align-items: center;
          outline: none;
          transform: translateY(var(--y)) translateZ(0);
          transition: transform .4s ease, box-shadow .4s ease;
          box-sizing: border-box;
          opacity: 1;

          @media #{$sm} {
            font-size: 20px;
          }

          &:hover {
            color: var(--secondary);
          }

          &.mean-expand {
            width: 100%;
            height: 48px;
            justify-content: end;
            font-weight: 300;
            border: none !important;
            background: transparent;
            color: var(--primary);

            @media #{$sm} {
              height: 50px;
            }

            &:hover {
              opacity: 1;
            }
          }
        }

        li {
          &:first-child {
            border-top: 1px solid var(--border);
          }

          a {
            font-size: 16px;
            text-transform: capitalize;
            border-top: none !important;
            padding: 12px 0;
            padding-inline-start: 15px;

            &.mean-expand {
              height: 58px;

              @media #{$md} {
                height: 25px;
              }

              @media #{$sm} {
                height: 22px;
              }
            }
          }

          li {
            &:last-child {
              border-bottom: 1px solid var(--border);
            }

            a {
              padding-left: 40px;
            }
          }
        }
      }
    }

    .mean-bar {
      padding: 0;
      background: none;
      max-height: auto;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        width: 0;
      }
    }

    a.meanmenu-reveal {
      display: none !important;
    }
  }
}

/* body style  */
.body-page-inner {
  position: relative;
  z-index: 100;
  background-color: #FFFFFF;

  &.dark {
    .header-area-2 .side-toggle {
      background-color: #1D1C1C;
    }
  }

  .container {
    &.large {
      @media (min-width: 1650px) {
        max-width: 1650px;
        --container-max-widths: 1620px;
        --bs-gutter-x: 30px;
      }
    }
  }

  .header-area-2 .side-toggle {
    background-color: rgba(243, 243, 243, 1);

    @include dark {
      background-color: #1D1C1C;
    }
  }

  .header-area-2__inner {
    border-bottom: 1px solid var(--border);
  }
}

// sticky header style
.header-sticky {
  transition: all 0.5s;
}

.transformed {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 800;
  transform: translateY(-100%);

  .header-area__inner {
    height: 80px;
  }

}

.sticky {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 800;
  background-color: #FFFFFF;
  transform: translateY(0%);

  .header-area__inner {
    height: 80px;
  }
}

/* page title style  */
.page-title-wrapper {
  margin-top: 68px;
  margin-bottom: 28px;

  @media #{$md} {
    margin-bottom: 10px;
  }

  .page-title {
    font-family: var(--font_thunder);
    font-size: 495px;
    font-weight: 600;
    line-height: 0.80;
    letter-spacing: -0.02em;
    text-align: center;
    text-transform: uppercase;

    @media #{$xxl} {
      font-size: 385px;
    }

    @media #{$xl} {
      font-size: 325px;
    }

    @media #{$lg} {
      font-size: 265px;
    }

    @media #{$md} {
      font-size: 185px;
    }

    @media #{$sm} {
      font-size: 125px;
    }

    @media #{$xs} {
      font-size: 90px;
    }
  }
}