/* Preloader css */
.container-preloader {
  align-items: center;
  cursor: none;
  display: flex;
  width: 100vw;
  height: 100vh;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 900;

  .animation-preloader {
    position: absolute;
    z-index: 100;

    .spinner {
      animation: t-spinner 1s infinite linear;
      border-radius: 50%;
      height: 9em;
      width: 9em;
      border: 10px solid var(--primary);
      border-top-color: var(--white);
      margin: 0 auto 3.5em auto;

      @include dark {
        border-top-color: var(--black);
      }

      @media #{$xxl} {
        height: 5em;
        width: 5em;
        border-width: 5px;
      }

      @media #{$sm} {
        margin: 0 auto .2em auto;
      }
    }

    .txt-loading {
      font: bold 5em 'Montserrat', sans-serif;
      text-align: center;
      user-select: none;

      .characters {
        color: var(--white);
        position: relative;
        display: inline-block;


        @media #{$sm} {
          font-size: 50px;
        }

        &:before {
          color: var(--primary);
          content: attr(data-text);
          animation: t-characters 4s infinite;
          left: 0;
          top: 0;
          opacity: 0;
          position: absolute;
          transform: rotateY(-90deg);
        }

        &:nth-child(2):before {
          animation-delay: 0.2s;
        }

        &:nth-child(3):before {
          animation-delay: 0.4s;
        }

        &:nth-child(4):before {
          animation-delay: 0.6s;
        }

        &:nth-child(5):before {
          animation-delay: 0.8s;
        }

        &:nth-child(6):before {
          animation-delay: 1s;
        }
      }
    }
  }

  .loader-section {
    background-color: var(--white);
    height: 100%;
    position: fixed;
    top: 0;
    width: calc(50% + 1px);

    @include dark {
      background-color: var(--black);
    }

    &.section-left {
      left: 0;
    }

    &.section-right {
      right: 0;
    }
  }
}

.loaded {
  .animation-preloader {
    opacity: 0;
    transition: 0.3s ease-out;
  }

  .loader-section {
    &.section-left {
      transform: translateX(-101%);
      transition: 0.7s 0.3s all cubic-bezier(0.1, 0.1, 0.1, 1.000);
    }

    &.section-right {
      transform: translateX(101%);
      transition: 0.7s 0.3s all cubic-bezier(0.1, 0.1, 0.1, 1.000);
    }
  }
}