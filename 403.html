<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Redox HTML Template - 403 Forbidden">

  <title>403 - Access Denied | Redox - Creative Agency</title>

  <!-- Fav Icon -->
  <link rel="icon" type="image/x-icon" href="/assets/imgs/logo/favicon.png">

  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="/assets/vendor/bootstrap.min.css">
  <link rel="stylesheet" href="/assets/vendor/fontawesome.min.css">
  <link rel="stylesheet" href="/assets/vendor/swiper-bundle.min.css">
  <link rel="stylesheet" href="/assets/vendor/meanmenu.min.css">
  <link rel="stylesheet" href="/assets/vendor/magnific-popup.css">
  <link rel="stylesheet" href="/assets/vendor/animate.min.css">

  <!-- Template Main CSS File -->
  <link rel="stylesheet" href="/assets/css/style.css">

  <!-- Custom 403 Styles -->
  <style>
    .error-area-403 {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #111111 0%, #1a1a1a 100%);
      position: relative;
      overflow: hidden;
    }
    
    .error-area-403::before {
      content: "🚫";
      position: absolute;
      font-size: 20vw;
      opacity: 0.03;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
      50% { transform: translate(-50%, -45%) rotate(5deg); }
    }
    
    .error-content-403 {
      text-align: center;
      position: relative;
      z-index: 2;
      max-width: 800px;
      padding: 0 20px;
    }
    
    .error-code-403 {
      font-family: var(--font_thunder);
      font-size: clamp(120px, 15vw, 300px);
      font-weight: 700;
      line-height: 0.8;
      color: var(--theme);
      text-transform: uppercase;
      margin-bottom: 20px;
      text-shadow: 0 0 30px rgba(255, 106, 58, 0.3);
      animation: pulse 2s ease-in-out infinite;
      cursor: pointer;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.8; }
    }
    
    .error-title-403 {
      font-family: var(--font_sequelsansromanbody);
      font-size: clamp(32px, 5vw, 60px);
      font-weight: 310;
      line-height: 1.1;
      color: var(--white);
      margin-bottom: 30px;
      letter-spacing: -0.02em;
    }
    
    .error-subtitle-403 {
      font-family: var(--font_dmsans);
      font-size: clamp(16px, 2vw, 20px);
      font-weight: 400;
      line-height: 1.5;
      color: #999999;
      margin-bottom: 40px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .error-actions-403 {
      display: flex;
      justify-content: center;
      margin-top: 50px;
    }
    
    .btn-403 {
      display: inline-flex;
      align-items: center;
      gap: 10px;
      padding: 18px 35px;
      background: transparent;
      border: 2px solid var(--theme);
      color: var(--theme);
      text-decoration: none;
      font-family: var(--font_dmsans);
      font-size: 16px;
      font-weight: 500;
      border-radius: 50px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .btn-403:hover {
      color: var(--white);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(255, 106, 58, 0.3);
    }
    
    .btn-403::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: var(--theme);
      transition: left 0.3s ease;
      z-index: -1;
    }
    
    .btn-403:hover::before {
      left: 0;
    }
    
    @media (max-width: 768px) {
      .btn-403 {
        width: 100%;
        max-width: 300px;
        justify-content: center;
      }
    }
  </style>

</head>

<body class="body-wrapper body-page-inner font-heading-sequelsans-romanbody">

  <!-- Preloader -->
  <div id="preloader">
    <div id="container" class="container-preloader">
      <div class="animation-preloader">
        <div class="spinner"></div>
      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
  </div>

  <!-- Scroll to top -->
  <div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"></path>
    </svg>
  </div>

  <div class="has-smooth" id="has_smooth"></div>
  <div id="smooth-wrapper">
    <div id="smooth-content">

      <main>
        <!-- 403 error area start -->
        <section class="error-area-403">
          <div class="error-content-403">
            <div class="error-code-403">403</div>
            
            <h1 class="error-title-403">Well, well, well... Look who's being naughty!</h1>

            <p class="error-subtitle-403">
              Looks like you've wandered into restricted territory.
              Nothing to see here, move along! 😏
            </p>

            <div class="error-actions-403">
              <a href="index.html" class="btn-403">
                <i class="fas fa-home"></i>
                Go Back Home
              </a>
            </div>
          </div>
        </section>
        <!-- 403 error area end -->

      </main>

    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="/assets/vendor/jquery-3.7.1.min.js"></script>
  <script src="/assets/vendor/bootstrap.bundle.min.js"></script>
  <script src="/assets/vendor/jquery.magnific-popup.min.js"></script>
  <script src="/assets/vendor/swiper-bundle.min.js"></script>
  <script src="/assets/vendor/gsap.min.js"></script>
  <script src="/assets/vendor/ScrollTrigger.min.js"></script>
  <script src="/assets/vendor/ScrollSmoother.min.js"></script>
  <script src="/assets/vendor/ScrollToPlugin.min.js"></script>
  <script src="/assets/vendor/SplitText.min.js"></script>
  <script src="/assets/vendor/TextPlugin.js"></script>
  <script src="/assets/vendor/customEase.js"></script>
  <script src="/assets/vendor/Flip.min.js"></script>
  <script src="/assets/vendor/jquery.meanmenu.min.js"></script>
  <script src="/assets/vendor/backToTop.js"></script>
  <script src="/assets/vendor/matter.js"></script>
  <script src="/assets/vendor/throwable.js"></script>
  <script src="/assets/js/magiccursor.js"></script>

  <!-- Template Main JS File -->
  <script src="/assets/js/main.js"></script>

  <!-- Custom 403 JavaScript -->
  <script>
    // Simple easter egg for the error code
    document.addEventListener('DOMContentLoaded', function() {
      const errorCode = document.querySelector('.error-code-403');
      let clickCount = 0;

      errorCode.addEventListener('click', function() {
        clickCount++;
        if (clickCount === 3) {
          this.style.transform = 'rotate(360deg)';
          this.style.transition = 'transform 1s ease';

          setTimeout(() => {
            alert("🎉 Nice try! But you're still not getting in. 😏");
            this.style.transform = 'rotate(0deg)';
          }, 1000);

          clickCount = 0;
        }
      });
    });
  </script>

</body>

</html>
