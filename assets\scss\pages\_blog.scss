/* blog page css */
.blog-area-2 {
    .section-header {
        border-top: 1px solid var(--border);
        padding-top: 37px;

        @media #{$md} {
            padding-top: 7px;
        }
    }

    .section-title-wrapper {
        display: grid;
        gap: 15px 60px;
        grid-template-columns: 1fr 1235px;

        @media #{$xxl} {
            grid-template-columns: 1fr 1000px;
        }

        @media #{$xl} {
            grid-template-columns: 1fr 850px;
        }

        @media #{$lg} {
            grid-template-columns: 1fr 750px;
        }

        @media #{$md} {
            grid-template-columns: 1fr;
        }
    }

    .subtitle-wrapper {
        margin-top: 8px;
    }

    .section-title {
        max-width: 800px;

        @media #{$xxl} {
            max-width: 700px;
        }

        @media #{$xl} {
            max-width: 640px;
        }
    }

    .blogs-wrapper-box {
        margin-top: 94px;

        @media #{$xxl} {
            margin-top: 64px;
        }

        @media #{$md} {
            margin-top: 44px;
        }
    }

    .blogs-wrapper {
        display: grid;
        gap: 76px 60px;
        grid-template-columns: repeat(6, 1fr);
        overflow: hidden;

        @media #{$xxl} {
            gap: 46px 40px;
        }

        @media #{$md} {
            grid-template-columns: repeat(2, 1fr);
        }

        @media #{$xs} {
            grid-template-columns: repeat(1, 1fr);
        }

        >* {
            grid-column: span 2;

            @media #{$md} {
                grid-column: auto;
            }

            &:nth-child(4),
            &:nth-child(5) {
                grid-column: span 3;

                @media #{$md} {
                    grid-column: auto;
                }

                .content {
                    padding-right: 130px;

                    @media #{$xxl} {
                        padding-right: 100px;
                    }

                    @media #{$lg} {
                        padding-right: 80px;
                    }

                    @media #{$md} {
                        padding-right: 0;
                    }
                }
            }

            .content {
                padding-right: 90px;

                @media #{$xxl} {
                    padding-right: 70px;
                }

                @media #{$lg} {
                    padding-right: 0;
                }
            }
        }
    }

    .blog {
        position: relative;

        &:hover {
            .thumb {
                img {
                    transform: scale(1.1);
                }
            }

            .title {
                .arrow {
                    background-color: var(--primary);

                    svg {
                        transform: rotate(60deg);

                        * {
                            fill: var(--white);

                            @include dark {
                                fill: var(--black);
                            }
                        }
                    }
                }
            }
        }

        &:before {
            position: absolute;
            content: "";
            width: 1px;
            height: 100%;
            background-color: var(--border);
            top: 0;
            left: -30px;

            @media #{$xxl} {
                left: -20px;
            }
        }

        .thumb {
            overflow: hidden;

            img {
                width: 100%;
                transition: all 0.5s;
            }
        }

        .content {
            margin-top: 24px;


            @media #{$lg} {
                margin-top: 14px;
            }
        }

        .title {
            font-size: 36px;
            font-weight: 310;
            line-height: 1;
            letter-spacing: -0.07em;
            display: inline;

            @media #{$xxl} {
                font-size: 30px;
            }

            @media #{$xl} {
                font-size: 24px;
            }

            @media #{$lg} {
                font-size: 20px;
            }

            .arrow {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 25px;
                height: 25px;
                transition: all 0.3s;
                border-radius: 50%;
                border: 2px solid var(--primary);
                transform: translate(0px, -1px);
                margin-left: 5px;

                @media #{$xxl} {
                    width: 20px;
                    height: 20px;
                }

                @media #{$xl} {
                    width: 17px;
                    height: 17px;
                }

                @media #{$lg} {
                    width: 15px;
                    height: 15px;
                    border-width: 1px;
                }

                svg {
                    transition: all 0.3s;
                    width: 13px;

                    @media #{$xxl} {
                        width: 10px;
                    }

                    @media #{$xl} {
                        width: 7px;
                    }

                    * {
                        fill: var(--primary);
                    }
                }
            }
        }

        .meta {
            display: flex;
            gap: 5px;
            align-items: center;
            margin-top: 14px;

            @media #{$lg} {
                margin-top: 9px;
            }

            span {
                font-size: 14px;
                font-weight: 400;
                line-height: 24px;
                color: var(--secondary);

                &.has-left-line {
                    padding-inline-start: 15px;

                    &:before {
                        width: 10px;
                    }
                }
            }

            .name span {
                font-weight: 500;
                color: var(--primary);
            }
        }
    }
}