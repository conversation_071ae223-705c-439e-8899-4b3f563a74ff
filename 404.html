<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Redox HTML Template - 404 Page Not Found">

  <title>404 - Page Not Found | Redox - Creative Agency</title>

  <!-- Fav Icon -->
  <link rel="icon" type="image/x-icon" href="/assets/imgs/logo/favicon.png">

  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="/assets/vendor/bootstrap.min.css">
  <link rel="stylesheet" href="/assets/vendor/fontawesome.min.css">
  <link rel="stylesheet" href="/assets/vendor/swiper-bundle.min.css">
  <link rel="stylesheet" href="/assets/vendor/meanmenu.min.css">
  <link rel="stylesheet" href="/assets/vendor/magnific-popup.css">
  <link rel="stylesheet" href="/assets/vendor/animate.min.css">

  <!-- Template Main CSS File -->
  <link rel="stylesheet" href="/assets/css/style.css">

  <!-- Custom 404 Styles -->
  <style>
    .error-area-404 {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      position: relative;
      overflow: hidden;
    }

    .error-area-404::before {
      content: "🔍";
      position: absolute;
      font-size: 20vw;
      opacity: 0.03;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      animation: search 4s ease-in-out infinite;
    }

    @keyframes search {
      0%, 100% { transform: translate(-50%, -50%) scale(1); }
      50% { transform: translate(-50%, -50%) scale(1.1); }
    }

    .error-content-404 {
      text-align: center;
      position: relative;
      z-index: 2;
      max-width: 800px;
      padding: 0 20px;
    }

    .error-code-404 {
      font-family: var(--font_thunder);
      font-size: clamp(120px, 15vw, 300px);
      font-weight: 700;
      line-height: 0.8;
      color: var(--theme);
      text-transform: uppercase;
      margin-bottom: 20px;
      text-shadow: 0 0 30px rgba(255, 106, 58, 0.3);
      animation: bounce 3s ease-in-out infinite;
      cursor: pointer;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
    }

    .error-title-404 {
      font-family: var(--font_sequelsansromanbody);
      font-size: clamp(32px, 5vw, 60px);
      font-weight: 310;
      line-height: 1.1;
      color: var(--primary);
      margin-bottom: 30px;
      letter-spacing: -0.02em;
    }

    .error-subtitle-404 {
      font-family: var(--font_dmsans);
      font-size: clamp(16px, 2vw, 20px);
      font-weight: 400;
      line-height: 1.5;
      color: #666666;
      margin-bottom: 40px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .error-actions-404 {
      display: flex;
      justify-content: center;
      margin-top: 50px;
    }


  </style>

</head>

<body class="body-wrapper body-page-inner font-heading-sequelsans-romanbody">

  <!-- Preloader -->
  <div id="preloader">
    <div id="container" class="container-preloader">
      <div class="animation-preloader">
        <div class="spinner"></div>

      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
    </div>
  </div>

  <!-- Sroll to top -->
  <div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
      <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"></path>
    </svg>
  </div>

  <!-- side toggle start -->
  <aside class="fix">
    <div class="side-info">
      <div class="side-info-content">
        <div class="offset-widget offset-header">
          <div class="offset-logo">
            <a href="index.html">
              <img src="assets/imgs/logo/logo-2.png" alt="site logo">
            </a>
          </div>
          <button id="side-info-close" class="side-info-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mobile-menu d-xl-none fix"></div>
        <div class="offset-button">
          <a href="contact.html" class="rr-btn">
            <span class="btn-wrap">
              <span class="text-one">Let's Talk</span>
              <span class="text-two">Let's Talk</span>
            </span>
          </a>
        </div>
        <div class="offset-widget-box">
          <h2 class="title">Contact US</h2>
          <div class="contact-meta">
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-location-dot"></i></span>
              <span class="text">3891 Ranchview Dr. Richardson</span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-envelope"></i></span>
              <span class="text"><a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
            <div class="contact-item">
              <span class="icon"><i class="fa-solid fa-phone"></i></span>
              <span class="text"><a href="tel:(505)555-0125">(*************</a></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
  <div class="offcanvas-overlay"></div>
  <!-- side toggle end -->

  <!-- Header area start -->
  <header></header>
  <!-- Header area end -->

  <div class="has-smooth" id="has_smooth"></div>
  <div id="smooth-wrapper">
    <div id="smooth-content">

      <main>

        <!-- 404 error area start -->
        <section class="error-area-404">
          <div class="error-content-404">
            <div class="error-code-404">404</div>

            <h1 class="error-title-404">Oops! Page not found</h1>

            <p class="error-subtitle-404">
              Looks like this page decided to take a little break.
              No worries, let's get you back on track! 🚀
            </p>

            <div class="error-actions-404">
              <a href="index.html" class="rr-btn">
                <span class="btn-wrap">
                  <span class="text-one">Back to Home</span>
                  <span class="text-two">Back to Home</span>
                </span>
              </a>
            </div>
          </div>
        </section>
        <!-- 404 error area end -->

      </main>

      <!-- footer area start  -->
      <footer></footer>
      <!-- footer area end  -->

    </div>
  </div>

  <!-- Vendor JS Files -->
  <script src="/assets/vendor/jquery-3.7.1.min.js"></script>
  <script src="/assets/vendor/bootstrap.bundle.min.js"></script>
  <script src="/assets/vendor/jquery.magnific-popup.min.js"></script>
  <script src="/assets/vendor/swiper-bundle.min.js"></script>
  <script src="/assets/vendor/gsap.min.js"></script>
  <script src="/assets/vendor/ScrollTrigger.min.js"></script>
  <script src="/assets/vendor/ScrollSmoother.min.js"></script>
  <script src="/assets/vendor/ScrollToPlugin.min.js"></script>
  <script src="/assets/vendor/SplitText.min.js"></script>
  <script src="/assets/vendor/TextPlugin.js"></script>
  <script src="/assets/vendor/customEase.js"></script>
  <script src="/assets/vendor/Flip.min.js"></script>
  <script src="/assets/vendor/jquery.meanmenu.min.js"></script>
  <script src="/assets/vendor/backToTop.js"></script>
  <script src="/assets/vendor/matter.js"></script>
  <script src="/assets/vendor/throwable.js"></script>
  <script src="/assets/js/magiccursor.js"></script>

  <!-- Template Main JS File -->
  <script src="/assets/js/main.js"></script>

  <!-- Custom 404 JavaScript -->
  <script>
    // Simple easter egg for the error code
    document.addEventListener('DOMContentLoaded', function() {
      const errorCode = document.querySelector('.error-code-404');
      let clickCount = 0;

      errorCode.addEventListener('click', function() {
        clickCount++;
        if (clickCount === 3) {
          this.style.transform = 'rotate(360deg) scale(1.1)';
          this.style.transition = 'transform 1s ease';

          setTimeout(() => {
            alert("🎉 You found the secret! But the page is still missing. 😄");
            this.style.transform = 'rotate(0deg) scale(1)';
          }, 1000);

          clickCount = 0;
        }
      });
    });
  </script>

</body>

</html>