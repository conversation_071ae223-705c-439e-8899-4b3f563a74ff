<?php
session_start();

class SecureContactForm {
    private $recipient;
    private $fromName;
    private $fromEmail;
    private $maxSubmissionsPerHour = 5;
    private $honeypotField = 'website'; // Hidden field for spam detection

    public function __construct($recipient, $fromName, $fromEmail) {
        $this->recipient = $recipient;
        $this->fromName = $fromName;
        $this->fromEmail = $fromEmail;
    }

    public function processSubmission($formData) {
        // Security checks
        if (!$this->validateCSRF($formData['csrf_token'] ?? '')) {
            return $this->errorResponse("Security validation failed. Please try again.", 403);
        }

        if (!$this->checkRateLimit()) {
            return $this->errorResponse("Too many submissions. Please wait before trying again.", 429);
        }

        if (!$this->checkHoneypot($formData[$this->honeypotField] ?? '')) {
            return $this->errorResponse("Spam detected.", 403);
        }

        // Validate and sanitize input
        $validatedData = $this->validateAndSanitizeInput($formData);
        if (!$validatedData) {
            return $this->errorResponse("Please fill in all required fields correctly.", 400);
        }

        // Send email
        return $this->sendEmail($validatedData);
    }

    private function validateCSRF($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    private function checkRateLimit() {
        $ip = $this->getClientIP();
        $currentTime = time();
        $sessionKey = 'submissions_' . md5($ip);

        if (!isset($_SESSION[$sessionKey])) {
            $_SESSION[$sessionKey] = [];
        }

        // Remove submissions older than 1 hour
        $_SESSION[$sessionKey] = array_filter($_SESSION[$sessionKey], function($timestamp) use ($currentTime) {
            return ($currentTime - $timestamp) < 3600;
        });

        if (count($_SESSION[$sessionKey]) >= $this->maxSubmissionsPerHour) {
            return false;
        }

        $_SESSION[$sessionKey][] = $currentTime;
        return true;
    }

    private function checkHoneypot($value) {
        return empty($value); // Honeypot should be empty
    }

    private function validateAndSanitizeInput($data) {
        $formType = $data['form_type'] ?? 'contact';
        $validated = ['form_type' => $formType];

        if ($formType === 'comment') {
            return $this->validateCommentForm($data);
        } else {
            return $this->validateContactForm($data);
        }
    }

    private function validateContactForm($data) {
        $required = ['name', 'email', 'message', 'solution'];
        $validated = ['form_type' => 'contact'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        // Sanitize and validate name
        $validated['name'] = $this->sanitizeString($data['name']);
        if (strlen($validated['name']) < 2 || strlen($validated['name']) > 100) {
            return false;
        }

        // Validate email
        $validated['email'] = filter_var(trim($data['email']), FILTER_VALIDATE_EMAIL);
        if (!$validated['email']) {
            return false;
        }

        // Sanitize message
        $validated['message'] = $this->sanitizeString($data['message']);
        if (strlen($validated['message']) < 10 || strlen($validated['message']) > 2000) {
            return false;
        }

        // Sanitize solution (required for contact form)
        $validated['solution'] = $this->sanitizeString($data['solution']);
        if (strlen($validated['solution']) < 2 || strlen($validated['solution']) > 200) {
            return false;
        }

        // Optional fields
        $validated['phone'] = isset($data['phone']) ? $this->sanitizeString($data['phone']) : '';
        $validated['company'] = isset($data['company']) ? $this->sanitizeString($data['company']) : '';
        $validated['budget'] = isset($data['Budget']) ? $this->sanitizeString($data['Budget']) : '';

        return $validated;
    }

    private function validateCommentForm($data) {
        $required = ['name', 'email', 'message'];
        $validated = ['form_type' => 'comment'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        // Sanitize and validate name
        $validated['name'] = $this->sanitizeString($data['name']);
        if (strlen($validated['name']) < 2 || strlen($validated['name']) > 100) {
            return false;
        }

        // Validate email
        $validated['email'] = filter_var(trim($data['email']), FILTER_VALIDATE_EMAIL);
        if (!$validated['email']) {
            return false;
        }

        // Sanitize message (shorter limit for comments)
        $validated['message'] = $this->sanitizeString($data['message']);
        if (strlen($validated['message']) < 5 || strlen($validated['message']) > 1000) {
            return false;
        }

        return $validated;
    }

    private function sanitizeString($input) {
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        $input = preg_replace('/[^\p{L}\p{N}\s\-\.\@\(\)\+]/u', '', $input);
        return $input;
    }

    private function sendEmail($data) {
        if ($data['form_type'] === 'comment') {
            $subject = "New Blog Comment from " . $data['name'];
            $successMessage = "Thank you! Your comment has been submitted successfully.";
        } else {
            $subject = "Contact Form Submission from " . $data['name'];
            $successMessage = "Thank you! Your message has been sent successfully.";
        }

        $email_content = $this->buildEmailContent($data);
        $email_headers = $this->buildEmailHeaders($data['email']);

        if (mail($this->recipient, $subject, $email_content, $email_headers)) {
            return $this->successResponse($successMessage);
        } else {
            error_log("Failed to send email from " . $data['form_type'] . " form: " . print_r($data, true));
            return $this->errorResponse("Sorry, there was an error sending your message. Please try again later.", 500);
        }
    }

    private function buildEmailContent($data) {
        if ($data['form_type'] === 'comment') {
            return $this->buildCommentEmailContent($data);
        } else {
            return $this->buildContactEmailContent($data);
        }
    }

    private function buildContactEmailContent($data) {
        $content = "New contact form submission:\n\n";
        $content .= "Name: " . $data['name'] . "\n";
        $content .= "Email: " . $data['email'] . "\n";

        if (!empty($data['phone'])) {
            $content .= "Phone: " . $data['phone'] . "\n";
        }

        if (!empty($data['company'])) {
            $content .= "Company: " . $data['company'] . "\n";
        }

        if (!empty($data['budget'])) {
            $content .= "Budget: " . $data['budget'] . "\n";
        }

        if (!empty($data['solution'])) {
            $content .= "Solution: " . $data['solution'] . "\n";
        }

        $content .= "\nMessage:\n" . $data['message'] . "\n\n";
        $content .= $this->getSubmissionFooter();

        return $content;
    }

    private function buildCommentEmailContent($data) {
        $content = "New blog comment submission:\n\n";
        $content .= "Name: " . $data['name'] . "\n";
        $content .= "Email: " . $data['email'] . "\n";
        $content .= "\nComment:\n" . $data['message'] . "\n\n";
        $content .= $this->getSubmissionFooter();

        return $content;
    }

    private function getSubmissionFooter() {
        $footer = "---\n";
        $footer .= "Submitted from: " . $this->getClientIP() . "\n";
        $footer .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
        $footer .= "Timestamp: " . date('Y-m-d H:i:s') . "\n";
        return $footer;
    }

    private function buildEmailHeaders($replyEmail) {
        $headers = "From: {$this->fromName} <{$this->fromEmail}>\r\n";
        $headers .= "Reply-To: {$replyEmail}\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        return $headers;
    }

    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }
        return 'Unknown';
    }

    private function successResponse($message) {
        http_response_code(200);
        return json_encode(['success' => true, 'message' => $message]);
    }

    private function errorResponse($message, $code = 400) {
        http_response_code($code);
        return json_encode(['success' => false, 'message' => $message]);
    }

    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

// Configuration
$recipient = "<EMAIL>";
$fromName = "Grumpy Company";
$fromEmail = "<EMAIL>";

$contactForm = new SecureContactForm($recipient, $fromName, $fromEmail);

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    header('Content-Type: application/json');

    try {
        $result = $contactForm->processSubmission($_POST);
        echo $result;
    } catch (Exception $e) {
        error_log("Contact form error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'An unexpected error occurred. Please try again later.']);
    }
} elseif ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['get_csrf'])) {
    // Endpoint to get CSRF token for AJAX requests
    header('Content-Type: application/json');
    echo json_encode(['csrf_token' => SecureContactForm::generateCSRFToken()]);
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed.']);
}
?>